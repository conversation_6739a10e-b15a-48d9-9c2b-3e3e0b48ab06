#!/usr/bin/env python3
"""
Script để chạy tất cả các test login trong dự án Robot Framework
"""

import os
import subprocess
import sys
from datetime import datetime

def run_robot_test(test_file):
    """Chạy một file test Robot Framework"""
    print(f"\n{'='*60}")
    print(f"🚀 Đang chạy test: {test_file}")
    print(f"{'='*60}")
    
    try:
        # Chạy robot command
        result = subprocess.run([
            'robot', 
            '--outputdir', 'Tests/Login/results',
            f'Tests/Login/{test_file}'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {test_file} - PASSED")
            return True
        else:
            print(f"❌ {test_file} - FAILED")
            print(f"Error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi chạy {test_file}: {str(e)}")
        return False

def main():
    """<PERSON><PERSON><PERSON> ch<PERSON>h để chạy tất cả test login"""
    print("🤖 Robot Framework Login Test Runner")
    print(f"⏰ Thời gian bắt đầu: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Tạo thư mục results nếu chưa có
    results_dir = "Tests/Login/results"
    os.makedirs(results_dir, exist_ok=True)
    
    # Danh sách các file test login
    test_files = [
        "LoginTest.robot"
    ]
    
    # Kiểm tra xem các file test có tồn tại không
    missing_files = []
    for test_file in test_files:
        if not os.path.exists(f"Tests/Login/{test_file}"):
            missing_files.append(test_file)
    
    if missing_files:
        print(f"⚠️  Không tìm thấy các file: {', '.join(missing_files)}")
        return
    
    # Chạy từng test
    results = {}
    for test_file in test_files:
        results[test_file] = run_robot_test(test_file)
    
    # Tổng kết kết quả
    print(f"\n{'='*60}")
    print("📊 TỔNG KẾT KẾT QUẢ")
    print(f"{'='*60}")
    
    passed = sum(1 for result in results.values() if result)
    failed = len(results) - passed
    
    for test_file, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_file:<25} - {status}")
    
    print(f"\n📈 Tổng cộng: {len(results)} tests")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"⏰ Thời gian kết thúc: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Hiển thị đường dẫn đến reports
    print(f"\n📁 Kết quả chi tiết tại: {results_dir}/")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
