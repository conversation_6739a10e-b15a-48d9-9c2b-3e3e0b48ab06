# Manual Test Cases
## CLO Management System v2.1

### Phi<PERSON><PERSON> bản: 2.1
### Ngày: Tháng 6/2025
### Tác giả: Nhóm QA CLO Management System

---

## 1. Authentication & Authorization

### TC_AUTH_001: <PERSON><PERSON><PERSON> nhập thành công với Admin
**<PERSON><PERSON><PERSON> tiêu**: <PERSON><PERSON><PERSON> tra đăng nhập admin thành công  
**Điều kiện tiên quyết**: <PERSON><PERSON> tài khoản admin hợp lệ  
**B<PERSON><PERSON>c thực hiện**:
1. <PERSON><PERSON><PERSON> cập trang chủ hệ thống
2. <PERSON>h<PERSON>p username admin hợp lệ
3. <PERSON><PERSON><PERSON><PERSON> password chính xác
4. <PERSON><PERSON><PERSON><PERSON> nút "Đăng nhập"

**Kết quả mong đợi**:
- <PERSON><PERSON><PERSON>n hướng đến dashboard
- Hiển thị menu đầy đủ cho admin
- Thông báo đăng nhập thành công
- <PERSON> được tạo thành công

### TC_AUTH_002: <PERSON><PERSON><PERSON> nhập thành công với Lecturer
**<PERSON><PERSON><PERSON> tiêu**: <PERSON><PERSON><PERSON> tra đăng nhập lecturer thành công  
**Điều kiện tiên quyết**: Có tài khoản lecturer hợp lệ  
**Bước thực hiện**:
1. Truy cập trang chủ hệ thống
2. Nhập username lecturer hợp lệ
3. Nhập password chính xác
4. Nhấn nút "Đăng nhập"

**Kết quả mong đợi**:
- Chuyển hướng đến dashboard
- Menu hiển thị phù hợp với quyền lecturer
- Không hiển thị các menu admin-only
- Session được tạo thành công

### TC_AUTH_003: Đăng nhập thất bại với thông tin sai
**Mục tiêu**: Kiểm tra xử lý đăng nhập thất bại  
**Điều kiện tiên quyết**: N/A  
**Bước thực hiện**:
1. Truy cập trang đăng nhập
2. Nhập username không tồn tại
3. Nhập password bất kỳ
4. Nhấn nút "Đăng nhập"

**Kết quả mong đợi**:
- Hiển thị thông báo lỗi rõ ràng
- Không chuyển hướng
- Form đăng nhập vẫn hiển thị
- Không tạo session

---

## 2. Program Management

### TC_PROGRAM_001: Tạo chương trình đào tạo mới
**Mục tiêu**: Kiểm tra tạo program thành công  
**Điều kiện tiên quyết**: Đăng nhập với quyền admin  
**Bước thực hiện**:
1. Truy cập "Academics" → "Programs"
2. Nhấn "Tạo chương trình mới"
3. Nhập thông tin:
   - Tên: "Kỹ thuật Phần mềm"
   - Mô tả: "Chương trình đào tạo kỹ sư phần mềm"
   - Khoa: Chọn từ dropdown
   - Ngày bắt đầu: Ngày hiện tại
   - Phiên bản: "2025"
4. Nhấn "Lưu chương trình"

**Kết quả mong đợi**:
- Chương trình được tạo thành công
- Hiển thị thông báo thành công
- Chuyển đến trang chi tiết program
- Program xuất hiện trong danh sách

### TC_PROGRAM_002: Xem chi tiết chương trình
**Mục tiêu**: Kiểm tra hiển thị chi tiết program  
**Điều kiện tiên quyết**: Có ít nhất 1 program trong hệ thống  
**Bước thực hiện**:
1. Truy cập danh sách programs
2. Nhấn vào tên một program
3. Kiểm tra các section hiển thị

**Kết quả mong đợi**:
- Thông tin program hiển thị đầy đủ
- Section "PLO Status Overview" hiển thị
- Color coding cho PLO status
- Nút "Sao chép chương trình" hiển thị ở header

### TC_PROGRAM_003: Sao chép chương trình đào tạo - Tính năng v2.1
**Mục tiêu**: Kiểm tra chức năng sao chép program hoàn chỉnh  
**Điều kiện tiên quyết**: 
- Đăng nhập với quyền admin
- Có program với PLOs, Courses, CLOs và mappings
**Bước thực hiện**:
1. Truy cập chi tiết một program có dữ liệu đầy đủ
2. Nhấn nút "Sao chép chương trình" ở header
3. Xác nhận trong modal dialog
4. Chờ quá trình sao chép hoàn thành

**Kết quả mong đợi**:
- Modal xác nhận hiển thị
- Quá trình sao chép thành công
- Thông báo chi tiết: "Đã tạo: X PLO, Y học phần, Z CLO, W ánh xạ PLO-CLO"
- Chuyển đến trang chi tiết program mới
- Program mới có tên gốc + "(Copy)"
- Tất cả dữ liệu được sao chép: PLOs, Courses, CLOs, PIs, PLO-CLO mappings

### TC_PROGRAM_004: Cập nhật thông tin chương trình
**Mục tiêu**: Kiểm tra chỉnh sửa program  
**Điều kiện tiên quyết**: Đăng nhập admin, có program để chỉnh sửa  
**Bước thực hiện**:
1. Từ danh sách programs, nhấn "Chỉnh sửa"
2. Thay đổi thông tin (tên, mô tả)
3. Nhấn "Cập nhật"

**Kết quả mong đợi**:
- Thông tin được cập nhật thành công
- Thông báo xác nhận
- Dữ liệu mới hiển thị chính xác

---

## 3. PLO Management & Visual Status

### TC_PLO_001: Tạo PLO cho chương trình
**Mục tiêu**: Kiểm tra tạo PLO thành công  
**Điều kiện tiên quyết**: Có program, đăng nhập admin  
**Bước thực hiện**:
1. Truy cập chi tiết program
2. Nhấn "Thêm PLO"
3. Nhập thông tin:
   - Mã PLO: "PLO1"
   - Mô tả: "Sinh viên có thể phân tích yêu cầu hệ thống"
   - Danh mục: "Skill"
   - Mức độ: "Analyze"
   - Phiên bản: "1.0"
4. Lưu PLO

**Kết quả mong đợi**:
- PLO được tạo thành công
- PLO hiển thị trong danh sách
- Trạng thái "Chưa mapping" (màu đỏ)

### TC_PLO_002: Kiểm tra Visual Status Indicators - Tính năng v2.1
**Mục tiêu**: Kiểm tra color coding cho PLO status  
**Điều kiện tiên quyết**: 
- Có PLO đã ánh xạ với môn cốt lõi
- Có PLO chưa ánh xạ với môn cốt lõi
**Bước thực hiện**:
1. Truy cập trang chi tiết program
2. Quan sát section "PLO Status Overview"
3. Kiểm tra màu sắc và icons

**Kết quả mong đợi**:
- PLO đã ánh xạ: màu xanh (#28a745) + icon check
- PLO chưa ánh xạ: màu đỏ (#dc3545) + icon warning
- Badge hiển thị "Đã mapping" hoặc "Chưa mapping"
- Thống kê: "X/Y PLO đã có ánh xạ với môn cốt lõi"

### TC_PLO_003: Tạo Performance Indicators cho PLO
**Mục tiêu**: Kiểm tra tạo PI cho PLO  
**Điều kiện tiên quyết**: Có PLO, đăng nhập admin  
**Bước thực hiện**:
1. Từ trang PLO, nhấn "Thêm PI"
2. Nhập thông tin:
   - Mã PI: "PLO1.1"
   - Mô tả: "Có thể xác định stakeholders"
   - Tiêu chí đo lường: "Liệt kê được ít nhất 5 stakeholders"
3. Lưu PI

**Kết quả mong đợi**:
- PI được tạo và liên kết với PLO
- PI hiển thị trong danh sách
- Relationship PLO-PI được maintain

---

## 4. Course & CLO Management

### TC_COURSE_001: Tạo học phần mới
**Mục tiêu**: Kiểm tra tạo course thành công  
**Điều kiện tiên quyết**: Có program, đăng nhập admin  
**Bước thực hiện**:
1. Từ program, nhấn "Thêm học phần"
2. Nhập thông tin:
   - Mã: "SE101"
   - Tên: "Nhập môn Kỹ thuật Phần mềm"
   - Số tín chỉ: 3
   - Loại: "Bắt buộc"
   - Tích "Môn cốt lõi": true
3. Lưu course

**Kết quả mong đợi**:
- Course được tạo thành công
- Hiển thị trong danh sách courses
- Flag "is_core" = true được lưu
- Course xuất hiện trong Core Subject Matrix

### TC_COURSE_002: Tạo CLO cho học phần
**Mục tiêu**: Kiểm tra tạo CLO cho course  
**Điều kiện tiên quyết**: Có course, đăng nhập admin/lecturer  
**Bước thực hiện**:
1. Truy cập chi tiết course
2. Vào tab "CLO Management"
3. Nhấn "Thêm CLO"
4. Nhập thông tin:
   - Mã CLO: "CLO1"
   - Mô tả: "Hiểu được khái niệm SDLC"
   - Danh mục: "Knowledge"
   - Mức độ: "Understand"
   - Trọng số: 25
   - Phương pháp đánh giá: "Exam"
5. Lưu CLO

**Kết quả mong đợi**:
- CLO được tạo thành công
- CLO hiển thị trong danh sách
- Trọng số và phương pháp đánh giá được lưu chính xác

### TC_COURSE_003: Quản lý Course Contents
**Mục tiêu**: Kiểm tra tạo nội dung môn học  
**Điều kiện tiên quyết**: Có course với CLOs  
**Bước thực hiện**:
1. Truy cập chi tiết course
2. Vào tab "Course Contents"
3. Nhấn "Thêm nội dung"
4. Nhập:
   - Số chương: 1
   - Tiêu đề: "Giới thiệu SDLC"
   - Mô tả: "Overview về Software Development Life Cycle"
   - Số giờ: 3
   - Phương pháp dạy: "Lecture + Discussion"
5. Map với CLO và lưu

**Kết quả mong đợi**:
- Course content được tạo
- Mapping với CLO thành công
- Hiển thị trong lịch trình môn học

---

## 5. PLO-CLO Mapping CRUD - Tính năng v2.1

### TC_MAPPING_001: Tạo ánh xạ PLO-CLO mới
**Mục tiêu**: Kiểm tra tạo mapping từ CLO detail page  
**Điều kiện tiên quyết**: 
- Có CLO và PLO cùng program
- Đăng nhập với quyền phù hợp
**Bước thực hiện**:
1. Truy cập trang chi tiết CLO
2. Nhấn nút "Map CLO to PLO"
3. Trong modal/form:
   - Chọn PLO từ dropdown (chỉ hiển thị PLO cùng program)
   - Chọn mức độ ánh xạ: "High"
4. Nhấn "Save Mapping"

**Kết quả mong đợi**:
- Mapping được tạo thành công
- Hiển thị trong danh sách mappings của CLO
- Thông báo "PLO-CLO Mapping created successfully!"
- PLO status được cập nhật (màu xanh nếu là core course)

### TC_MAPPING_002: Cập nhật ánh xạ PLO-CLO
**Mục tiêu**: Kiểm tra chỉnh sửa mapping level  
**Điều kiện tiên quyết**: Có mapping PLO-CLO tồn tại  
**Bước thực hiện**:
1. Tại trang chi tiết CLO
2. Nhấn icon "Edit" bên cạnh mapping
3. Thay đổi mức độ ánh xạ từ "High" thành "Medium"
4. Nhấn "Update"

**Kết quả mong đợi**:
- Mapping được cập nhật thành công
- Mức độ mới hiển thị chính xác
- Thông báo xác nhận cập nhật
- Database được update đúng

### TC_MAPPING_003: Xóa ánh xạ PLO-CLO
**Mục tiêu**: Kiểm tra xóa mapping  
**Điều kiện tiên quyết**: Có mapping để xóa  
**Bước thực hiện**:
1. Tại trang chi tiết CLO
2. Nhấn icon "Delete" bên cạnh mapping
3. Xác nhận trong modal dialog
4. Nhấn "Confirm Delete"

**Kết quả mong đợi**:
- Modal xác nhận hiển thị thông tin mapping
- Mapping bị xóa khỏi danh sách
- Thông báo xác nhận
- PLO status được cập nhật (có thể chuyển sang màu đỏ)

### TC_MAPPING_004: Validation cho mapping trùng lặp
**Mục tiêu**: Kiểm tra validation duplicate mapping  
**Điều kiện tiên quyết**: Đã có mapping PLO1-CLO1  
**Bước thực hiện**:
1. Truy cập trang chi tiết CLO1
2. Thử tạo mapping mới với PLO1 (đã exist)
3. Chọn mức độ bất kỳ
4. Nhấn "Save"

**Kết quả mong đợi**:
- Hiển thị lỗi validation
- Không tạo duplicate mapping
- Form vẫn hiển thị để sửa lỗi
- Message rõ ràng về lỗi duplicate

---

## 6. Question Bank & Enhanced Permissions - Tính năng v2.1

### TC_QUESTION_001: Tạo câu hỏi với quyền Creator
**Mục tiêu**: Kiểm tra tạo câu hỏi thành công  
**Điều kiện tiên quyết**: Đăng nhập lecturer, có course với CLOs  
**Bước thực hiện**:
1. Truy cập "Question Bank" → "Create Question"
2. Nhập thông tin:
   - Course: Chọn course
   - CLO: Chọn CLO tương ứng
   - Loại: "Multiple Choice"
   - Độ khó: "Medium"
   - Câu hỏi: "Python sử dụng cú pháp nào để khai báo biến?"
   - Đáp án A: "var x = 5" 
   - Đáp án B: "x = 5" (đúng)
   - Đáp án C: "int x = 5"
   - Đáp án D: "declare x = 5"
3. Lưu câu hỏi

**Kết quả mong đợi**:
- Câu hỏi được tạo thành công
- Creator có quyền edit câu hỏi này
- CLO mapping được lưu chính xác

### TC_QUESTION_002: Kiểm tra quyền edit theo Teaching Assignment
**Mục tiêu**: Kiểm tra lecturer có thể edit câu hỏi từ course họ dạy  
**Điều kiện tiên quyết**: 
- Lecturer A giảng dạy Course X
- Có câu hỏi thuộc Course X được tạo bởi người khác
**Bước thực hiện**:
1. Đăng nhập với Lecturer A
2. Truy cập danh sách câu hỏi của Course X
3. Kiểm tra nút "Edit" có hiển thị không
4. Thử chỉnh sửa câu hỏi

**Kết quả mong đợi**:
- Nút "Edit" hiển thị cho câu hỏi thuộc course lecturer dạy
- Có thể chỉnh sửa thành công
- Không hiển thị nút edit cho câu hỏi course khác

### TC_QUESTION_003: Kiểm tra quyền Admin full access
**Mục tiêu**: Kiểm tra admin có thể edit tất cả câu hỏi  
**Điều kiện tiên quyết**: Đăng nhập admin, có câu hỏi của nhiều users  
**Bước thực hiện**:
1. Truy cập danh sách tất cả câu hỏi
2. Kiểm tra nút "Edit" trên mọi câu hỏi
3. Thử chỉnh sửa câu hỏi do lecturer tạo

**Kết quả mong đợi**:
- Admin thấy nút "Edit" trên tất cả câu hỏi
- Có thể chỉnh sửa bất kỳ câu hỏi nào
- Thay đổi được lưu thành công

### TC_QUESTION_004: Kiểm tra phân quyền từ chối access
**Mục tiêu**: Kiểm tra lecturer không thể edit câu hỏi không có quyền  
**Điều kiện tiên quyết**: 
- Lecturer B không dạy Course Y
- Có câu hỏi thuộc Course Y do người khác tạo
**Bước thực hiện**:
1. Đăng nhập với Lecturer B
2. Truy cập câu hỏi thuộc Course Y
3. Kiểm tra có nút "Edit" không
4. Thử truy cập trực tiếp URL edit

**Kết quả mong đợi**:
- Không hiển thị nút "Edit"
- Truy cập trực tiếp URL edit bị từ chối (403)
- Thông báo lỗi quyền truy cập rõ ràng

---

## 7. Test & Assessment Management

### TC_TEST_001: Tạo bài kiểm tra mới
**Mục tiêu**: Kiểm tra tạo test thành công  
**Điều kiện tiên quyết**: Có course class, đăng nhập lecturer  
**Bước thực hiện**:
1. Truy cập chi tiết lớp học
2. Nhấn "Tạo bài kiểm tra"
3. Nhập thông tin:
   - Tên: "Midterm Exam SE101"
   - Loại: "Midterm"
   - Thời gian: 90 phút
   - Tổng điểm: 100
   - Hình thức thi: "Written"
4. Lưu test

**Kết quả mong đợi**:
- Test được tạo thành công
- Hiển thị trong danh sách tests của lớp
- Thông tin chi tiết chính xác

### TC_TEST_002: Thêm câu hỏi vào bài kiểm tra
**Mục tiêu**: Kiểm tra thêm questions vào test  
**Điều kiện tiên quyết**: Có test và questions  
**Bước thực hiện**:
1. Truy cập chi tiết test
2. Nhấn "Thêm câu hỏi"
3. Chọn câu hỏi từ question bank
4. Nhập điểm cho câu hỏi
5. Map với CLO
6. Lưu

**Kết quả mong đợi**:
- Câu hỏi được thêm vào test
- Điểm số và CLO mapping được lưu
- Tổng điểm test được cập nhật

---

## 8. Class Management

### TC_CLASS_001: Tạo lớp học mới
**Mục tiêu**: Kiểm tra tạo class thành công  
**Điều kiện tiên quyết**: Có course, đăng nhập admin  
**Bước thực hiện**:
1. Từ course detail, nhấn "Tạo lớp"
2. Nhập thông tin:
   - Tên lớp: "SE101.01"
   - Giảng viên: Chọn từ danh sách
   - Học kỳ: "1"
   - Năm học: "2024-2025"
   - Sĩ số tối đa: 30
3. Lưu lớp

**Kết quả mong đợi**:
- Lớp học được tạo thành công
- Giảng viên được assign chính xác
- Hiển thị trong danh sách classes

### TC_CLASS_002: Đăng ký sinh viên vào lớp
**Mục tiêu**: Kiểm tra enrollment sinh viên  
**Điều kiện tiên quyết**: Có lớp học và danh sách sinh viên  
**Bước thực hiện**:
1. Truy cập chi tiết lớp
2. Nhấn "Quản lý sinh viên"
3. Nhấn "Thêm sinh viên"
4. Chọn sinh viên từ danh sách
5. Xác nhận đăng ký

**Kết quả mong đợi**:
- Sinh viên được đăng ký thành công
- Hiển thị trong danh sách lớp
- Enrollment date được ghi lại

---

## 9. Reporting & Analytics

### TC_REPORT_001: Xem báo cáo CLO
**Mục tiêu**: Kiểm tra report CLO performance  
**Điều kiện tiên quyết**: Có dữ liệu test results  
**Bước thực hiện**:
1. Truy cập "Reports" → "CLO Reports"
2. Chọn course và class
3. Xem báo cáo chi tiết

**Kết quả mong đợi**:
- Báo cáo hiển thị CLO performance
- Charts và graphs hiển thị chính xác
- Có thể export báo cáo

### TC_REPORT_002: Phân tích bài kiểm tra
**Mục tiêu**: Kiểm tra test analysis  
**Điều kiện tiên quyết**: Có test với results  
**Bước thực hiện**:
1. Truy cập "Reports" → "Test Analysis"
2. Chọn test cần phân tích
3. Xem detailed analysis

**Kết quả mong đợi**:
- Statistics hiển thị đúng
- Item analysis chính xác
- Performance metrics tính toán đúng

---

## 10. System Integration & Performance

### TC_SYSTEM_001: Kiểm tra responsive design
**Mục tiêu**: Kiểm tra giao diện trên mobile  
**Điều kiện tiên quyết**: N/A  
**Bước thực hiện**:
1. Truy cập hệ thống trên mobile browser
2. Test các chức năng chính
3. Kiểm tra layout và navigation

**Kết quả mong đợi**:
- Giao diện responsive tốt
- Các chức năng hoạt động bình thường
- Navigation menu dễ sử dụng

### TC_SYSTEM_002: Kiểm tra đa ngôn ngữ
**Mục tiêu**: Kiểm tra chuyển đổi ngôn ngữ  
**Điều kiện tiên quyết**: Hệ thống hỗ trợ VI/EN  
**Bước thực hiện**:
1. Chuyển từ tiếng Việt sang tiếng Anh
2. Kiểm tra nội dung hiển thị
3. Test các chức năng trong cả 2 ngôn ngữ

**Kết quả mong đợi**:
- Chuyển đổi ngôn ngữ mượt mà
- Nội dung được dịch chính xác
- Formatting và layout không bị lỗi

### TC_SYSTEM_003: Kiểm tra performance với dữ liệu lớn
**Mục tiêu**: Kiểm tra hiệu suất hệ thống  
**Điều kiện tiên quyết**: Database có nhiều dữ liệu  
**Bước thực hiện**:
1. Thực hiện sao chép program có nhiều dữ liệu
2. Load trang với nhiều records
3. Kiểm tra thời gian response

**Kết quả mong đợi**:
- Response time < 5 seconds cho operations thường
- Sao chép program hoàn thành trong thời gian hợp lý
- Không có timeout errors

---

## 11. Error Handling & Edge Cases

### TC_ERROR_001: Kiểm tra xử lý database connection error
**Mục tiêu**: Kiểm tra graceful handling khi mất kết nối DB  
**Điều kiện tiên quyết**: Có khả năng simulate DB disconnect  
**Bước thực hiện**:
1. Disconnect database
2. Thử thực hiện operations
3. Kiểm tra error messages

**Kết quả mong đợi**:
- Hiển thị error message thân thiện
- Không crash application
- Có suggestion để user retry

### TC_ERROR_002: Kiểm tra validation errors
**Mục tiêu**: Kiểm tra form validation  
**Điều kiện tiên quyết**: N/A  
**Bước thực hiện**:
1. Submit form với dữ liệu invalid
2. Để trống required fields
3. Nhập dữ liệu sai format

**Kết quả mong đợi**:
- Validation errors hiển thị rõ ràng
- Focus vào field có lỗi
- Error messages hữu ích

### TC_ERROR_003: Kiểm tra session timeout
**Mục tiêu**: Kiểm tra xử lý session hết hạn  
**Điều kiện tiên quyết**: Session timeout được config  
**Bước thực hiện**:
1. Đăng nhập và chờ session expire
2. Thử thực hiện action yêu cầu authentication
3. Kiểm tra redirect behavior

**Kết quả mong đợi**:
- Redirect đến login page
- Thông báo session expired
- Có thể đăng nhập lại bình thường

---

## 12. Security Testing

### TC_SECURITY_001: Kiểm tra SQL Injection
**Mục tiêu**: Kiểm tra bảo vệ chống SQL injection  
**Điều kiện tiên quyết**: N/A  
**Bước thực hiện**:
1. Thử nhập SQL commands vào search fields
2. Test với các payload injection phổ biến
3. Kiểm tra response

**Kết quả mong đợi**:
- Không thực hiện SQL commands
- Input được sanitize properly
- Không leak database information

### TC_SECURITY_002: Kiểm tra Cross-Site Scripting (XSS)
**Mục tiêu**: Kiểm tra bảo vệ chống XSS  
**Điều kiện tiên quyết**: N/A  
**Bước thực hiện**:
1. Thử nhập script tags vào text fields
2. Submit forms với JavaScript code
3. Kiểm tra output rendering

**Kết quả mong đợi**:
- Script tags được escape
- Không execute malicious code
- Output hiển thị an toàn

### TC_SECURITY_003: Kiểm tra Authorization bypass
**Mục tiêu**: Kiểm tra không thể bypass quyền truy cập  
**Điều kiện tiên quyết**: Có nhiều user với quyền khác nhau  
**Bước thực hiện**:
1. Đăng nhập với user thường
2. Thử truy cập trực tiếp URL admin-only
3. Thử modify requests để access restricted data

**Kết quả mong đợi**:
- Access bị từ chối (403/401)
- Không thể view restricted data
- Proper error messages

---

## Test Data Requirements

### Dữ liệu cần chuẩn bị:
1. **Users**: 
   - 1 Admin account
   - 2-3 Lecturer accounts
   - 5-10 Student accounts

2. **Academic Data**:
   - 2-3 Programs với PLOs đầy đủ
   - 5-8 Courses với CLOs
   - PLO-CLO mappings (một số đã map, một số chưa)
   - Performance Indicators cho PLOs

3. **Question Bank**:
   - 20-30 questions nhiều loại khác nhau
   - Questions thuộc courses khác nhau
   - Questions được tạo bởi users khác nhau

4. **Classes & Tests**:
   - 3-5 Course classes với enrollments
   - 2-3 Tests với questions và results
   - Test submissions với scores

### Test Environment Setup:
- Database với sample data đầy đủ
- Multiple browser support (Chrome, Firefox, Safari)
- Mobile testing devices/simulators
- Network throttling capabilities
- Logging enabled để track issues

---

## Test Execution Guidelines

### Trước khi test:
1. Backup database
2. Clear browser cache
3. Verify test data integrity
4. Document environment details

### Trong quá trình test:
1. Ghi lại screenshots cho failed cases
2. Note down exact error messages
3. Record steps to reproduce issues
4. Test trên multiple browsers

### Sau khi test:
1. Document all findings
2. Categorize issues by severity
3. Provide detailed reproduction steps
4. Suggest fixes where possible

### Bug Report Template:
- **Bug ID**: Unique identifier
- **Test Case**: TC_XXX_XXX
- **Severity**: Critical/High/Medium/Low
- **Description**: Clear summary
- **Steps to Reproduce**: Detailed steps
- **Expected Result**: What should happen
- **Actual Result**: What actually happened
- **Environment**: Browser, OS, etc.
- **Screenshots**: Visual evidence
- **Additional Notes**: Any other relevant info