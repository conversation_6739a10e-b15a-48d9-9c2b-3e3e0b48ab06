<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3.1 (Python 3.10.18 on linux)" generated="2025-07-04T08:19:44.610547" rpa="false" schemaversion="5">
<suite id="s1" name="AdminLoginTest" source="/home/<USER>/Desktop/eme/learn-auto-eme/Tests/Login/AdminLoginTest.robot">
<test id="s1-t1" name="Login As Admin" line="8">
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-07-04T08:19:44.700577" level="INFO">Opening browser 'Chrome' to base url 'https://demo-clo.minds.vn'.</msg>
<arg>${URL}</arg>
<arg>${BROWSER}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-07-04T08:19:44.691404" elapsed="4.046332"/>
</kw>
<kw name="Login With Credentials" owner="LoginPage">
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-04T08:19:48.743180" level="INFO">Typing text 'admin' into text field 'xpath=//input[@name='username']'.</msg>
<arg>${USERNAME_INPUT}</arg>
<arg>${username}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T08:19:48.741619" elapsed="0.151835"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-04T08:19:48.894580" level="INFO">Typing text 'demoadmin' into text field 'xpath=//input[@name='password']'.</msg>
<arg>${PASSWORD_INPUT}</arg>
<arg>${password}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T08:19:48.893907" elapsed="0.089483"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-07-04T08:19:48.984502" level="INFO">Clicking button 'xpath=//button[text()='Đăng nhập']'.</msg>
<arg>${LOGIN_BUTTON}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T08:19:48.983906" elapsed="0.062593"/>
</kw>
<arg>${ADMIN_USER}</arg>
<arg>${ADMIN_PASS}</arg>
<status status="PASS" start="2025-07-04T08:19:48.739762" elapsed="0.307220"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-04T08:19:59.048746" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-04T08:19:49.047724" elapsed="10.001367"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary" type="TEARDOWN">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-07-04T08:19:59.050218" elapsed="0.067699"/>
</kw>
<status status="PASS" start="2025-07-04T08:19:44.690383" elapsed="14.427806"/>
</test>
<status status="PASS" start="2025-07-04T08:19:44.611120" elapsed="14.507947"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="AdminLoginTest" id="s1" pass="1" fail="0" skip="0">AdminLoginTest</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
