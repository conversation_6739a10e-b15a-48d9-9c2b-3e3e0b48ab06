<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3.1 (Python 3.10.18 on linux)" generated="2025-07-04T08:31:35.247799" rpa="false" schemaversion="5">
<suite id="s1" name="AdminLoginTest" source="/home/<USER>/Desktop/eme/learn-auto-eme/Tests/Login/AdminLoginTest.robot">
<test id="s1-t1" name="Login As Admin" line="8">
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-07-04T08:31:35.337331" level="INFO">Opening browser 'Chrome' to base url 'https://demo-clo.minds.vn'.</msg>
<arg>${URL}</arg>
<arg>${BROWSER}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-07-04T08:31:35.329762" elapsed="3.154590"/>
</kw>
<kw name="Login With Credentials" owner="LoginPage">
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-04T08:31:38.488668" level="INFO">Typing text 'admin' into text field 'xpath=//input[@name='username']'.</msg>
<arg>${USERNAME_INPUT}</arg>
<arg>${username}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T08:31:38.487424" elapsed="0.105640"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-04T08:31:38.593660" level="INFO">Typing text 'demoadmin' into text field 'xpath=//input[@name='password']'.</msg>
<arg>${PASSWORD_INPUT}</arg>
<arg>${password}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T08:31:38.593297" elapsed="0.061220"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-07-04T08:31:38.655566" level="INFO">Clicking button 'xpath=//button[text()='Đăng nhập']'.</msg>
<arg>${LOGIN_BUTTON}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T08:31:38.655006" elapsed="0.048722"/>
</kw>
<arg>${ADMIN_USER}</arg>
<arg>${ADMIN_PASS}</arg>
<status status="PASS" start="2025-07-04T08:31:38.485494" elapsed="0.218522"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${USER_INFO}</arg>
<arg>10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T08:31:38.704451" elapsed="3.738863"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-07-04T08:31:42.472052" level="INFO">${USER_INFO_TEXT} = Xin chào, admin</msg>
<var>${USER_INFO_TEXT}</var>
<arg>${USER_INFO}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T08:31:42.443634" elapsed="0.028474"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${USER_INFO_TEXT}</arg>
<arg>Xin chào, admin</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2025-07-04T08:31:42.472349" elapsed="0.000376"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T08:31:42.485895" level="INFO">Element 'xpath=//h1[text()='Dashboard']' is displayed.</msg>
<arg>${H1_DASHBOARD}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T08:31:42.472988" elapsed="0.012994"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T08:31:42.495634" level="INFO">Element 'xpath=//a[@href="/admin/"]' is displayed.</msg>
<arg>${MENU_ADMIN}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T08:31:42.486170" elapsed="0.009552"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T08:31:42.510891" level="INFO">Element 'xpath=//div[@class='menu-section-title' and text()='Hệ thống']' is displayed.</msg>
<arg>${TEXT_MENU_ADMIN}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T08:31:42.495905" elapsed="0.015075"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-04T08:31:52.511647" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-04T08:31:42.511161" elapsed="10.000816"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary" type="TEARDOWN">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-07-04T08:31:52.513284" elapsed="0.061610"/>
</kw>
<status status="PASS" start="2025-07-04T08:31:35.329086" elapsed="17.245937"/>
</test>
<status status="PASS" start="2025-07-04T08:31:35.248506" elapsed="17.326993"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="AdminLoginTest" id="s1" pass="1" fail="0" skip="0">AdminLoginTest</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
