*** Settings ***
Library    SeleniumLibrary
Resource    ../../Resources/PageObject/KeywordDefinationFiles/LoginPage.robot
Resource    ../../Resources/PageObject/Locators/LoginLocators.robot
Resource    ../../Resources/PageObject/TestData/Accounts.robot

*** Test Cases ***
Login As invalid Account
    Open Browser    ${URL}    ${BROWSER}

    # Login
    Login With Credentials    ${INVALID_USER}    ${INVALID_PASS}

    # Check notify
    Wait Until Element Is Visible    ${NOTIFY_LOGIN_FAILD}    10s
    ${TEXT}=    Get Text    ${NOTIFY_LOGIN_FAILD}
    Should Contain    ${TEXT}    ${NOTIFY_LOGIN_FAILD_TEXT}

    # Check for admin menus that should not exist
    Element Should Not Be Visible    ${H1_DASHBOARD}
    Element Should Not Be Visible    ${MENU_ADMIN}
    Element Should Not Be Visible    ${TEXT_MENU_ADMIN}

    # Check for login element that should exist
    Element Should Be Visible    ${USERNAME_INPUT}
    Element Should Be Visible    ${PASSWORD_INPUT}
    Element Should Be Visible    ${LOGIN_BUTTON}

    # Exe logout
    Sleep    1s
    [Teardown]    Close Browser