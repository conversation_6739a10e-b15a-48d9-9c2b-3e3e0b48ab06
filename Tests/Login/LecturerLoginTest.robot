*** Settings ***
Library    SeleniumLibrary
Resource    ../../Resources/PageObject/KeywordDefinationFiles/LoginPage.robot
Resource    ../../Resources/PageObject/Locators/LoginLocators.robot
Resource    ../../Resources/PageObject/TestData/Accounts.robot

*** Test Cases ***
Login As Lecturer
    Open Browser    ${URL}    ${BROWSER}

    # Login
    Login With Credentials    ${LECTURER_USER}    ${LECTURER_PASS}
    Wait Until Element Is Visible    ${USER_INFO}    10s
    ${COOKIE_COUNT}=    Get Cookie Count
    Should Be True    ${COOKIE_COUNT} > 3

    # Check notify
    ${NOTIFY_TEXT}=    Get Text    ${NOTIFY_LOGIN}
    Should Contain    ${NOTIFY_TEXT}    Chào mừng Giảng viên
    Sleep    1s

    Element Should Be Visible    ${H1_DASHBOARD}
    # Check for admin menus that should not exist
    Element Should Not Be Visible    ${MENU_ADMIN}
    Element Should Not Be Visible    ${TEXT_MENU_ADMIN}

    # Exe logout
    Logout Normal
    Sleep    1s
    [Teardown]    Close Browser