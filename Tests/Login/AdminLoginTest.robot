*** Settings ***
Library    SeleniumLibrary
Resource    ../../Resources/PageObject/KeywordDefinationFiles/LoginPage.robot
Resource    ../../Resources/PageObject/Locators/LoginLocators.robot
Resource    ../../Resources/PageObject/TestData/Accounts.robot

*** Test Cases ***
Login As Admin
    Open Browser    ${URL}    ${BROWSER}

    Login With Credentials    ${ADMIN_USER}    ${ADMIN_PASS}
    ${COOKIE_COUNT}=    Get Cookie Count
    Should Be True    ${COOKIE_COUNT} > 3

    Wait Until Element Is Visible    ${USER_INFO}    10s
    ${USER_INFO_TEXT}=    Get Text    ${USER_INFO}
    Should Be Equal    ${USER_INFO_TEXT}    Xin chào, admin

    ${NOTIFY_TEXT}=    Get Text    ${NOTIFY_LOGIN}
    Should Contain    ${NOTIFY_TEXT}    Chào mừng Quản trị viên !

    # <PERSON><PERSON><PERSON> tra sự tồn tại của các menu dành cho admin
    Element Should Be Visible    ${H1_DASHBOARD}
    Element Should Be Visible    ${MENU_ADMIN}
    Element Should Be Visible    ${TEXT_MENU_ADMIN}

    # Thực hiện logout
    Logout Normal

    Sleep    1s
    [Teardown]    Close Browser
