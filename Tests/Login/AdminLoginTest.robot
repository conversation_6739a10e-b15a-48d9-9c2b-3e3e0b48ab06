*** Settings ***
Library    SeleniumLibrary
Resource    ../../Resources/PageObject/KeywordDefinationFiles/LoginPage.robot
Resource    ../../Resources/PageObject/Locators/LoginLocators.robot
Resource    ../../Resources/PageObject/TestData/Accounts.robot

*** Test Cases ***
Login As Admin
    Open Browser    ${URL}    ${BROWSER}
    Login With Credentials    ${ADMIN_USER}    ${ADMIN_PASS}
    Sleep    10s
    [Teardown]    Close Browser
