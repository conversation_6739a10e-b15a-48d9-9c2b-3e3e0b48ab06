*** Settings ***
Library    SeleniumLibrary
Resource    ../../Resources/PageObject/KeywordDefinationFiles/LoginPage.robot
Resource    ../../Resources/PageObject/Locators/LoginLocators.robot
Resource    ../../Resources/PageObject/TestData/Accounts.robot

*** Test Cases ***
Login As Admin
    Open Browser    ${URL}    ${BROWSER}
    Login With Credentials    ${ADMIN_USER}    ${ADMIN_PASS}
    Wait Until Element Is Visible    ${USER_INFO}    10s
    ${USER_INFO_TEXT}=    Get Text    ${USER_INFO}
    Should Be Equal    ${USER_INFO_TEXT}    Xin chào, admin
    Sleep    10s
    [Teardown]    Close Browser

    
