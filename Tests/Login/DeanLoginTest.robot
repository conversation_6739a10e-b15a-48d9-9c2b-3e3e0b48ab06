*** Settings ***
Library    SeleniumLibrary
Resource    ../../Resources/PageObject/KeywordDefinationFiles/LoginPage.robot
Resource    ../../Resources/PageObject/Locators/LoginLocators.robot
Resource    ../../Resources/PageObject/TestData/Accounts.robot

*** Test Cases ***
Login As Dean
    Open Browser    ${URL}    ${BROWSER}
    ${COOKIE_COUNT}=    Get Cookie Count
    Should Be True    ${COOKIE_COUNT} <= 3

    # Login
    Login With Credentials    ${DEAN_USER}    ${DEAN_PASS}
    Wait Until Element Is Visible    ${USER_INFO}    10s
    ${COOKIE_COUNT}=    Get Cookie Count
    Should Be True    ${COOKIE_COUNT} > 3

    # Kiểm tra user info admin
    ${USER_INFO_TEXT}=    Get Text    ${USER_INFO}
    Should Be Equal    ${USER_INFO_TEXT}    Xin chào, Trần Văn Khoa
    Sleep    2s

    # Thực hiện logout
    Logout Normal
    ${COOKIE_COUNT}=    Get Cookie Count
    Should Be True    ${COOKIE_COUNT} <= 3
    Sleep    2s
    [Teardown]    Close Browser
