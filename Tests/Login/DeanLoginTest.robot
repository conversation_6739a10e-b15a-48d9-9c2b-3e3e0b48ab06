*** Settings ***
Library    SeleniumLibrary
Resource    ../../Resources/PageObject/KeywordDefinationFiles/LoginPage.robot
Resource    ../../Resources/PageObject/Locators/LoginLocators.robot
Resource    ../../Resources/PageObject/TestData/Accounts.robot

*** Test Cases ***
Login As Dean
    Open Browser    ${URL}    ${BROWSER}

    # Login
    Login With Credentials    ${DEAN_USER}    ${DEAN_PASS}
    Wait Until Element Is Visible    ${USER_INFO}    10s
    ${COOKIE_COUNT}=    Get Cookie Count
    Should Be True    ${COOKIE_COUNT} > 3

    # Check user info dean
    ${USER_INFO_TEXT}=    Get Text    ${USER_INFO}
    Should Be Equal    ${USER_INFO_TEXT}    Xin chào, Trần Văn Khoa

    Element Should Be Visible    ${H1_DASHBOARD}
    # Check for admin menus that should not exist
    Element Should Not Be Visible    ${H1_DASHBOARD}
    Element Should Not Be Visible    ${MENU_ADMIN}
    Element Should Not Be Visible    ${TEXT_MENU_ADMIN}

    # Exe logout
    Logout Normal
    Sleep    1s
    [Teardown]    Close Browser
