<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3.1 (Python 3.10.18 on linux)" generated="2025-07-04T12:55:16.018417" rpa="false" schemaversion="5">
<suite id="s1" name="LoginTest" source="/home/<USER>/Desktop/eme/learn-auto-eme/Tests/Login/LoginTest.robot">
<test id="s1-t1" name="Login As Admin" line="8">
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:16.105546" level="INFO">Opening browser 'Chrome' to base url 'https://demo-clo.minds.vn'.</msg>
<arg>${URL}</arg>
<arg>${BROWSER}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-07-04T12:55:16.105285" elapsed="7.303798"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-04T12:55:25.410781" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-04T12:55:23.409938" elapsed="2.001186"/>
</kw>
<kw name="Get Cookie Count" owner="LoginPage">
<kw name="Get Cookies" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:25.424604" level="INFO">${cookies} = csrftoken=vYhzHCc7V83gbgd6NmbIM7iqQKVyfCY8; django_language=en; sessionid=tysag9g8d4pqd8tqbmtkupp9paox55f1</msg>
<var>${cookies}</var>
<doc>Returns all cookies of the current page.</doc>
<status status="PASS" start="2025-07-04T12:55:25.413199" elapsed="0.011548"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-07-04T12:55:25.425772" level="INFO">Length is 106.</msg>
<msg time="2025-07-04T12:55:25.426042" level="INFO">${count} = 106</msg>
<var>${count}</var>
<arg>${cookies}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-07-04T12:55:25.425227" elapsed="0.000900"/>
</kw>
<return>
<value>${count}</value>
<status status="PASS" start="2025-07-04T12:55:25.426345" elapsed="0.000167"/>
</return>
<msg time="2025-07-04T12:55:25.427004" level="INFO">${COOKIE_COUNT} = 106</msg>
<var>${COOKIE_COUNT}</var>
<status status="PASS" start="2025-07-04T12:55:25.412071" elapsed="0.015026"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<msg time="2025-07-04T12:55:25.428445" level="FAIL">'106 &lt;= 3' should be true.</msg>
<arg>${COOKIE_COUNT} &lt;= 3</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="FAIL" start="2025-07-04T12:55:25.427480" elapsed="0.001235">'106 &lt;= 3' should be true.</status>
</kw>
<kw name="Login With Credentials" owner="LoginPage">
<arg>${ADMIN_USER}</arg>
<arg>${ADMIN_PASS}</arg>
<status status="NOT RUN" start="2025-07-04T12:55:25.429513" elapsed="0.000173"/>
</kw>
<kw name="Get Cookie Count" owner="LoginPage">
<var>${COOKIE_COUNT}</var>
<status status="NOT RUN" start="2025-07-04T12:55:25.430424" elapsed="0.000199"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${COOKIE_COUNT} &gt; 3</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="NOT RUN" start="2025-07-04T12:55:25.431020" elapsed="0.000097"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${USER_INFO}</arg>
<arg>10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-07-04T12:55:25.431461" elapsed="0.000107"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<var>${USER_INFO_TEXT}</var>
<arg>${USER_INFO}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-07-04T12:55:25.431886" elapsed="0.000082"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${USER_INFO_TEXT}</arg>
<arg>Xin chào, admin</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="NOT RUN" start="2025-07-04T12:55:25.432323" elapsed="0.000089"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<var>${NOTIFY_TEXT}</var>
<arg>${NOTIFY_LOGIN}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-07-04T12:55:25.432736" elapsed="0.000079"/>
</kw>
<kw name="Should Contain" owner="BuiltIn">
<arg>${NOTIFY_TEXT}</arg>
<arg>Chào mừng Quản trị viên !</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="NOT RUN" start="2025-07-04T12:55:25.433125" elapsed="0.000082"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<arg>${H1_DASHBOARD}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-07-04T12:55:25.433533" elapsed="0.000081"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<arg>${MENU_ADMIN}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-07-04T12:55:25.433903" elapsed="0.000080"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<arg>${TEXT_MENU_ADMIN}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-07-04T12:55:25.434391" elapsed="0.000121"/>
</kw>
<kw name="Logout Normal" owner="LoginPage">
<status status="NOT RUN" start="2025-07-04T12:55:25.435039" elapsed="0.000116"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary" type="TEARDOWN">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-07-04T12:55:25.435932" elapsed="0.066880"/>
</kw>
<status status="FAIL" start="2025-07-04T12:55:16.104652" elapsed="9.398269">'106 &lt;= 3' should be true.</status>
</test>
<test id="s1-t2" name="Login As Dean" line="36">
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:25.503983" level="INFO">Opening browser 'Chrome' to base url 'https://demo-clo.minds.vn'.</msg>
<arg>${URL}</arg>
<arg>${BROWSER}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-07-04T12:55:25.503631" elapsed="2.762605"/>
</kw>
<kw name="Login With Credentials" owner="LoginPage">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-07-04T12:55:28.269091" elapsed="0.038758"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-04T12:55:32.309112" level="INFO">Slept 4 seconds.</msg>
<arg>4s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-04T12:55:28.308393" elapsed="4.001045"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:32.352816" level="INFO">Element 'xpath=//a[text()='Hide »']' is displayed.</msg>
<arg>${HIDE_TOOLBAR}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T12:55:32.310696" elapsed="0.042242"/>
</kw>
<msg time="2025-07-04T12:55:32.353110" level="INFO">${IS_VISIBLE} = True</msg>
<var>${IS_VISIBLE}</var>
<arg>Element Should Be Visible</arg>
<arg>${HIDE_TOOLBAR}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-04T12:55:32.310039" elapsed="0.043106"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:32.353817" level="INFO">Clicking element 'xpath=//a[text()='Hide »']'.</msg>
<arg>${HIDE_TOOLBAR}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:32.353596" elapsed="0.068304"/>
</kw>
<arg>${IS_VISIBLE}</arg>
<arg>Click Element</arg>
<arg>${HIDE_TOOLBAR}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-04T12:55:32.353329" elapsed="0.068898"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:32.423420" level="INFO">Typing text 'truongkhoa1' into text field 'xpath=//input[@name='username']'.</msg>
<arg>${USERNAME_INPUT}</arg>
<arg>${username}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:32.422703" elapsed="0.089978"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:32.514481" level="INFO">Typing text 'demotruongkhoa' into text field 'xpath=//input[@name='password']'.</msg>
<arg>${PASSWORD_INPUT}</arg>
<arg>${password}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:32.513297" elapsed="0.089084"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:32.603515" level="INFO">Clicking button 'xpath=//button[text()='Đăng nhập']'.</msg>
<arg>${LOGIN_BUTTON}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:32.602908" elapsed="0.059327"/>
</kw>
<arg>${DEAN_USER}</arg>
<arg>${DEAN_PASS}</arg>
<status status="PASS" start="2025-07-04T12:55:28.267114" elapsed="4.395456"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${USER_INFO}</arg>
<arg>10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T12:55:32.663122" elapsed="3.273883"/>
</kw>
<kw name="Get Cookie Count" owner="LoginPage">
<kw name="Get Cookies" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:35.940338" level="INFO">${cookies} = csrftoken=dsEQKoQ1YGFuP11zmqKrLrKd3WtRCZdj; sessionid=adds1ccxbh79o7few7kfjrwzridnz80n; django_language=en</msg>
<var>${cookies}</var>
<doc>Returns all cookies of the current page.</doc>
<status status="PASS" start="2025-07-04T12:55:35.937666" elapsed="0.002705"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-07-04T12:55:35.940616" level="INFO">Length is 106.</msg>
<msg time="2025-07-04T12:55:35.940679" level="INFO">${count} = 106</msg>
<var>${count}</var>
<arg>${cookies}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-07-04T12:55:35.940488" elapsed="0.000208"/>
</kw>
<return>
<value>${count}</value>
<status status="PASS" start="2025-07-04T12:55:35.940744" elapsed="0.000036"/>
</return>
<msg time="2025-07-04T12:55:35.940880" level="INFO">${COOKIE_COUNT} = 106</msg>
<var>${COOKIE_COUNT}</var>
<status status="PASS" start="2025-07-04T12:55:35.937302" elapsed="0.003598"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${COOKIE_COUNT} &gt; 3</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2025-07-04T12:55:35.940986" elapsed="0.000155"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:35.954038" level="INFO">${USER_INFO_TEXT} = Xin chào, Trần Văn Khoa</msg>
<var>${USER_INFO_TEXT}</var>
<arg>${USER_INFO}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:35.941234" elapsed="0.012848"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${USER_INFO_TEXT}</arg>
<arg>Xin chào, Trần Văn Khoa</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2025-07-04T12:55:35.954238" elapsed="0.000193"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:35.968571" level="INFO">Element 'xpath=//h1[text()='Dashboard']' is displayed.</msg>
<arg>${H1_DASHBOARD}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T12:55:35.954544" elapsed="0.014125"/>
</kw>
<kw name="Element Should Not Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:35.974194" level="INFO">Element 'xpath=//a[@href="/admin/"]' did not exist.</msg>
<arg>${MENU_ADMIN}</arg>
<doc>Verifies that the element identified by ``locator`` is NOT visible.</doc>
<status status="PASS" start="2025-07-04T12:55:35.968823" elapsed="0.005452"/>
</kw>
<kw name="Element Should Not Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:35.983103" level="INFO">Element 'xpath=//div[@class='menu-section-title' and text()='Hệ thống']' did not exist.</msg>
<arg>${TEXT_MENU_ADMIN}</arg>
<doc>Verifies that the element identified by ``locator`` is NOT visible.</doc>
<status status="PASS" start="2025-07-04T12:55:35.974416" elapsed="0.008939"/>
</kw>
<kw name="Logout Normal" owner="LoginPage">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:35.984566" level="INFO">Clicking element 'xpath=//button[contains(@class, 'btn-link')]'.</msg>
<arg>${SETTING_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:35.984312" elapsed="0.055287"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-04T12:55:38.040739" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-04T12:55:36.040045" elapsed="2.001043"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:38.042473" level="INFO">Clicking element 'xpath=//button[normalize-space(.)='Đăng xuất']'.</msg>
<arg>${LOGOUT_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:38.041667" elapsed="1.856607"/>
</kw>
<kw name="Wait Until Page Contains" owner="SeleniumLibrary">
<arg>Đăng nhập</arg>
<arg>10s</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" start="2025-07-04T12:55:39.898936" elapsed="0.016302"/>
</kw>
<status status="PASS" start="2025-07-04T12:55:35.983943" elapsed="3.931448"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary" type="TEARDOWN">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-07-04T12:55:39.915702" elapsed="0.062193"/>
</kw>
<status status="PASS" start="2025-07-04T12:55:25.503294" elapsed="14.474748"/>
</test>
<test id="s1-t3" name="Login As Lecturer" line="60">
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:39.979423" level="INFO">Opening browser 'Chrome' to base url 'https://demo-clo.minds.vn'.</msg>
<arg>${URL}</arg>
<arg>${BROWSER}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-07-04T12:55:39.978983" elapsed="2.598472"/>
</kw>
<kw name="Login With Credentials" owner="LoginPage">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-07-04T12:55:42.580029" elapsed="0.062702"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-04T12:55:46.643608" level="INFO">Slept 4 seconds.</msg>
<arg>4s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-04T12:55:42.643019" elapsed="4.000956"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:46.683268" level="INFO">Element 'xpath=//a[text()='Hide »']' is displayed.</msg>
<arg>${HIDE_TOOLBAR}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T12:55:46.645199" elapsed="0.038163"/>
</kw>
<msg time="2025-07-04T12:55:46.683497" level="INFO">${IS_VISIBLE} = True</msg>
<var>${IS_VISIBLE}</var>
<arg>Element Should Be Visible</arg>
<arg>${HIDE_TOOLBAR}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-04T12:55:46.644562" elapsed="0.038966"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:46.684113" level="INFO">Clicking element 'xpath=//a[text()='Hide »']'.</msg>
<arg>${HIDE_TOOLBAR}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:46.683898" elapsed="0.055559"/>
</kw>
<arg>${IS_VISIBLE}</arg>
<arg>Click Element</arg>
<arg>${HIDE_TOOLBAR}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-04T12:55:46.683674" elapsed="0.056061"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:46.740759" level="INFO">Typing text 'lecturer1' into text field 'xpath=//input[@name='username']'.</msg>
<arg>${USERNAME_INPUT}</arg>
<arg>${username}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:46.740105" elapsed="0.113166"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:46.853778" level="INFO">Typing text 'demolecturer' into text field 'xpath=//input[@name='password']'.</msg>
<arg>${PASSWORD_INPUT}</arg>
<arg>${password}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:46.853476" elapsed="0.049502"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:46.903804" level="INFO">Clicking button 'xpath=//button[text()='Đăng nhập']'.</msg>
<arg>${LOGIN_BUTTON}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:46.903327" elapsed="0.043603"/>
</kw>
<arg>${LECTURER_USER}</arg>
<arg>${LECTURER_PASS}</arg>
<status status="PASS" start="2025-07-04T12:55:42.578314" elapsed="4.369040"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${USER_INFO}</arg>
<arg>10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T12:55:46.947673" elapsed="2.980047"/>
</kw>
<kw name="Get Cookie Count" owner="LoginPage">
<kw name="Get Cookies" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:49.932703" level="INFO">${cookies} = csrftoken=lVgi58cggFk1Dw9XoLgWTy4pQHLeuk1C; sessionid=zwzrv22u8g12uv4et3ayje8tk4085vbi; django_language=en</msg>
<var>${cookies}</var>
<doc>Returns all cookies of the current page.</doc>
<status status="PASS" start="2025-07-04T12:55:49.928241" elapsed="0.004642"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-07-04T12:55:49.933887" level="INFO">Length is 106.</msg>
<msg time="2025-07-04T12:55:49.933980" level="INFO">${count} = 106</msg>
<var>${count}</var>
<arg>${cookies}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-07-04T12:55:49.933361" elapsed="0.000649"/>
</kw>
<return>
<value>${count}</value>
<status status="PASS" start="2025-07-04T12:55:49.934099" elapsed="0.000099"/>
</return>
<msg time="2025-07-04T12:55:49.934383" level="INFO">${COOKIE_COUNT} = 106</msg>
<var>${COOKIE_COUNT}</var>
<status status="PASS" start="2025-07-04T12:55:49.927979" elapsed="0.006427"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${COOKIE_COUNT} &gt; 3</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2025-07-04T12:55:49.934566" elapsed="0.000253"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:49.951109" level="INFO">${NOTIFY_TEXT} = Chào mừng Giảng viên Nguyễn Văn A!</msg>
<var>${NOTIFY_TEXT}</var>
<arg>${NOTIFY_LOGIN}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:49.934962" elapsed="0.016193"/>
</kw>
<kw name="Should Contain" owner="BuiltIn">
<arg>${NOTIFY_TEXT}</arg>
<arg>Chào mừng Giảng viên</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" start="2025-07-04T12:55:49.951355" elapsed="0.000219"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-04T12:55:50.951958" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-04T12:55:49.951682" elapsed="1.000603"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:50.997703" level="INFO">Element 'xpath=//h1[text()='Dashboard']' is displayed.</msg>
<arg>${H1_DASHBOARD}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T12:55:50.952914" elapsed="0.045211"/>
</kw>
<kw name="Element Should Not Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:51.013346" level="INFO">Element 'xpath=//a[@href="/admin/"]' did not exist.</msg>
<arg>${MENU_ADMIN}</arg>
<doc>Verifies that the element identified by ``locator`` is NOT visible.</doc>
<status status="PASS" start="2025-07-04T12:55:50.998779" elapsed="0.014799"/>
</kw>
<kw name="Element Should Not Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:51.021682" level="INFO">Element 'xpath=//div[@class='menu-section-title' and text()='Hệ thống']' did not exist.</msg>
<arg>${TEXT_MENU_ADMIN}</arg>
<doc>Verifies that the element identified by ``locator`` is NOT visible.</doc>
<status status="PASS" start="2025-07-04T12:55:51.013753" elapsed="0.008020"/>
</kw>
<kw name="Logout Normal" owner="LoginPage">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:51.022392" level="INFO">Clicking element 'xpath=//button[contains(@class, 'btn-link')]'.</msg>
<arg>${SETTING_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:51.022211" elapsed="0.059198"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-04T12:55:53.082501" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-04T12:55:51.081881" elapsed="2.000786"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:53.083643" level="INFO">Clicking element 'xpath=//button[normalize-space(.)='Đăng xuất']'.</msg>
<arg>${LOGOUT_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:55:53.083087" elapsed="1.702678"/>
</kw>
<kw name="Wait Until Page Contains" owner="SeleniumLibrary">
<arg>Đăng nhập</arg>
<arg>10s</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" start="2025-07-04T12:55:54.786144" elapsed="0.014688"/>
</kw>
<status status="PASS" start="2025-07-04T12:55:51.021970" elapsed="3.779174"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary" type="TEARDOWN">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-07-04T12:55:54.801722" elapsed="0.058409"/>
</kw>
<status status="PASS" start="2025-07-04T12:55:39.978418" elapsed="14.881799"/>
</test>
<test id="s1-t4" name="Login As invalid Account" line="85">
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-07-04T12:55:54.860914" level="INFO">Opening browser 'Chrome' to base url 'https://demo-clo.minds.vn'.</msg>
<arg>${URL}</arg>
<arg>${BROWSER}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-07-04T12:55:54.860687" elapsed="2.743695"/>
</kw>
<kw name="Login With Credentials" owner="LoginPage">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-07-04T12:55:57.606355" elapsed="0.038087"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-04T12:56:01.646413" level="INFO">Slept 4 seconds.</msg>
<arg>4s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-04T12:55:57.645347" elapsed="4.001433"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:56:01.686251" level="INFO">Element 'xpath=//a[text()='Hide »']' is displayed.</msg>
<arg>${HIDE_TOOLBAR}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T12:56:01.648019" elapsed="0.038352"/>
</kw>
<msg time="2025-07-04T12:56:01.686545" level="INFO">${IS_VISIBLE} = True</msg>
<var>${IS_VISIBLE}</var>
<arg>Element Should Be Visible</arg>
<arg>${HIDE_TOOLBAR}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-04T12:56:01.647341" elapsed="0.039234"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-04T12:56:01.687906" level="INFO">Clicking element 'xpath=//a[text()='Hide »']'.</msg>
<arg>${HIDE_TOOLBAR}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:56:01.687148" elapsed="0.069133"/>
</kw>
<arg>${IS_VISIBLE}</arg>
<arg>Click Element</arg>
<arg>${HIDE_TOOLBAR}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-04T12:56:01.686746" elapsed="0.069665"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-04T12:56:01.757071" level="INFO">Typing text 'invalidaccount123' into text field 'xpath=//input[@name='username']'.</msg>
<arg>${USERNAME_INPUT}</arg>
<arg>${username}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:56:01.756637" elapsed="0.063726"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-04T12:56:01.820814" level="INFO">Typing text 'invalidaccount123' into text field 'xpath=//input[@name='password']'.</msg>
<arg>${PASSWORD_INPUT}</arg>
<arg>${password}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:56:01.820557" elapsed="0.053431"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-07-04T12:56:01.874567" level="INFO">Clicking button 'xpath=//button[text()='Đăng nhập']'.</msg>
<arg>${LOGIN_BUTTON}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:56:01.874276" elapsed="0.045874"/>
</kw>
<arg>${INVALID_USER}</arg>
<arg>${INVALID_PASS}</arg>
<status status="PASS" start="2025-07-04T12:55:57.604853" elapsed="4.315610"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${NOTIFY_LOGIN_FAILD}</arg>
<arg>10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T12:56:01.920904" elapsed="1.639784"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-07-04T12:56:03.616326" level="INFO">${TEXT} = Please enter a correct username and password. Note that both fields may be case-sensitive.</msg>
<var>${TEXT}</var>
<arg>${NOTIFY_LOGIN_FAILD}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-04T12:56:03.561306" elapsed="0.055181"/>
</kw>
<kw name="Should Contain" owner="BuiltIn">
<arg>${TEXT}</arg>
<arg>${NOTIFY_LOGIN_FAILD_TEXT}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" start="2025-07-04T12:56:03.617049" elapsed="0.000792"/>
</kw>
<kw name="Element Should Not Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:56:03.631123" level="INFO">Element 'xpath=//h1[text()='Dashboard']' did not exist.</msg>
<arg>${H1_DASHBOARD}</arg>
<doc>Verifies that the element identified by ``locator`` is NOT visible.</doc>
<status status="PASS" start="2025-07-04T12:56:03.618312" elapsed="0.012900"/>
</kw>
<kw name="Element Should Not Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:56:03.636730" level="INFO">Element 'xpath=//a[@href="/admin/"]' did not exist.</msg>
<arg>${MENU_ADMIN}</arg>
<doc>Verifies that the element identified by ``locator`` is NOT visible.</doc>
<status status="PASS" start="2025-07-04T12:56:03.631362" elapsed="0.005448"/>
</kw>
<kw name="Element Should Not Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:56:03.642507" level="INFO">Element 'xpath=//div[@class='menu-section-title' and text()='Hệ thống']' did not exist.</msg>
<arg>${TEXT_MENU_ADMIN}</arg>
<doc>Verifies that the element identified by ``locator`` is NOT visible.</doc>
<status status="PASS" start="2025-07-04T12:56:03.636946" elapsed="0.005653"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:56:03.648821" level="INFO">Element 'xpath=//input[@name='username']' is displayed.</msg>
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T12:56:03.642733" elapsed="0.006171"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:56:03.654230" level="INFO">Element 'xpath=//input[@name='password']' is displayed.</msg>
<arg>${PASSWORD_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T12:56:03.649038" elapsed="0.005265"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-04T12:56:03.660106" level="INFO">Element 'xpath=//button[text()='Đăng nhập']' is displayed.</msg>
<arg>${LOGIN_BUTTON}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-04T12:56:03.654432" elapsed="0.005749"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary" type="TEARDOWN">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-07-04T12:56:03.660380" elapsed="0.055117"/>
</kw>
<status status="PASS" start="2025-07-04T12:55:54.860417" elapsed="8.855193"/>
</test>
<status status="FAIL" start="2025-07-04T12:55:16.018993" elapsed="47.697117"/>
</suite>
<statistics>
<total>
<stat pass="3" fail="1" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="LoginTest" id="s1" pass="3" fail="1" skip="0">LoginTest</stat>
</suite>
</statistics>
<errors>
<msg time="2025-07-04T12:55:16.102870" level="WARN">Error in file '/home/<USER>/Desktop/eme/learn-auto-eme/Resources/PageObject/KeywordDefinationFiles/LoginPage.robot' on line 18: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
</errors>
</robot>
