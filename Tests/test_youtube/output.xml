<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3.1 (Python 3.10.0 on win32)" generated="2025-07-03T20:45:31.041229" rpa="false" schemaversion="5">
<suite id="s1" name="Youtube" source="D:\Rainscales\learn_auto_eme\test_youtube\youtube.robot">
<test id="s1-t1" name="Search Son Tung On YouTube" line="10">
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-07-03T20:45:31.579837" level="INFO">Opening browser 'Chrome' to base url 'https://www.youtube.com'.</msg>
<arg>${URL}</arg>
<arg>${BROWSER}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-07-03T20:45:31.562011" elapsed="4.236080"/>
</kw>
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-07-03T20:45:35.799093" elapsed="0.281326"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${SEARCH_BOX}</arg>
<arg>10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T20:45:36.083421" elapsed="0.784937"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-03T20:45:36.870366" level="INFO">Typing text 'son tung' into text field '//input[@name='search_query']'.</msg>
<arg>${SEARCH_BOX}</arg>
<arg>son tung</arg>
<arg>5s</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T20:45:36.869357" elapsed="0.397506"/>
</kw>
<kw name="Press Keys" owner="SeleniumLibrary">
<msg time="2025-07-03T20:45:37.268862" level="INFO">Sending key(s) ('ENTER',) to //input[@name='search_query'] element.</msg>
<msg time="2025-07-03T20:45:37.740247" level="INFO">Pressing special key ENTER to element.</msg>
<msg time="2025-07-03T20:45:37.740247" level="INFO">Releasing special key ENTER.</msg>
<arg>${SEARCH_BOX}</arg>
<arg>ENTER</arg>
<doc>Simulates the user pressing key(s) to an element or on the active browser.</doc>
<status status="PASS" start="2025-07-03T20:45:37.266863" elapsed="0.560617"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T20:45:47.834247" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T20:45:37.828485" elapsed="10.005762"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-07-03T20:45:47.834247" elapsed="2.396106"/>
</kw>
<status status="PASS" start="2025-07-03T20:45:31.560000" elapsed="18.670353"/>
</test>
<status status="PASS" start="2025-07-03T20:45:31.043232" elapsed="19.188122"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Youtube" id="s1" pass="1" fail="0" skip="0">Youtube</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
