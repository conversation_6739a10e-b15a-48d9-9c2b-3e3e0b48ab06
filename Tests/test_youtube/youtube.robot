#*** Settings ***
#Library    SeleniumLibrary
#
#*** Variables ***
#${BROWSER}        Chrome
#${URL}            https://www.youtube.com
#${SEARCH_BOX}     //input[@name='search_query']
#
#*** Test Cases ***
#Search Son Tung On YouTube
#    Open Browser    ${URL}    ${BROWSER}
#    Maximize Browser Window
#    Wait Until Element Is Visible    ${SEARCH_BOX}    10s
#    Input Text    ${SEARCH_BOX}    son tung
#    Press Keys    ${SEARCH_BOX}    ENTER
#    Sleep    10s
#    Close Browser
