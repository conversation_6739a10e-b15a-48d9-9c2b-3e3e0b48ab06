# Hướng dẫn Sử dụng Hệ thống
## CLO Management System

### Phiên bản: 2.1 (Advanced CRUD & Program Duplication)
### Ngày: Tháng 6/2025
### Tác giả: Nh<PERSON>m phát triển CLO Management System

**Cập nhật phiên bản 2.1**:
- <PERSON><PERSON><PERSON> thiện chức năng CRUD cho tất cả thành phần hệ thống
- Tính năng sao chép chương trình đào tạo với toàn bộ dữ liệu
- Quản lý ánh xạ PLO-CLO với giao diện trực quan
- Hệ thống phân quyền nâng cao cho ngân hàng câu hỏi
- Trực quan hóa trạng thái ánh xạ bằng màu sắc
- Performance Indicators (PI) và ma trận môn cốt lõi
- Assessment components với trọng số và hỗ trợ multi-CLO

---

## 1. G<PERSON><PERSON><PERSON> thiệu

### 1.1 V<PERSON> hệ thống
CLO Management System là một ứng dụng web được thiết kế để hỗ trợ các trường đại học trong việc quản lý Course Learning Outcomes (CLO), tạo và quản lý ngân hàng câu hỏi, thiết kế bài kiểm tra và phân tích kết quả học tập.

### 1.2 Đối tượng sử dụng
- **Quản trị viên hệ thống**: Quản lý toàn bộ hệ thống
- **Giảng viên**: Tạo câu hỏi, thiết kế bài kiểm tra, đánh giá
- **Trưởng khoa**: Xem báo cáo, phê duyệt nội dung

### 1.3 Yêu cầu hệ thống
- **Trình duyệt**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Kết nối Internet**: Tốc độ tối thiểu 1 Mbps
- **Độ phân giải màn hình**: Tối thiểu 1024x768px
- **JavaScript**: Phải được bật

---

## 2. Đăng nhập và Quản lý Tài khoản

### 2.1 Đăng nhập hệ thống

1. **Truy cập trang đăng nhập**
   - Mở trình duyệt và truy cập URL hệ thống
   - Bạn sẽ được chuyển hướng đến trang đăng nhập

2. **Nhập thông tin đăng nhập**
   ```
   Username: [Tên đăng nhập của bạn]
   Password: [Mật khẩu của bạn]
   ```

3. **Xác thực và truy cập**
   - Nhấn nút "Đăng nhập"
   - Hệ thống sẽ kiểm tra thông tin và chuyển đến trang chủ

**Lưu ý**: Sau 5 lần đăng nhập sai, tài khoản sẽ bị khóa tạm thời 30 phút.

### 2.2 Chuyển đổi ngôn ngữ

Hệ thống hỗ trợ 2 ngôn ngữ:
- **Tiếng Việt** (mặc định)
- **English**

**Cách chuyển đổi**:
1. Nhấn vào biểu tượng ngôn ngữ ở góc trên bên phải
2. Chọn ngôn ngữ mong muốn
3. Trang sẽ tự động tải lại với ngôn ngữ mới

### 2.3 Quản lý thông tin cá nhân

1. **Truy cập trang profile**
   - Nhấn vào tên người dùng ở góc trên phải
   - Chọn "Profile" từ menu dropdown

2. **Cập nhật thông tin**
   - Chỉnh sửa các thông tin: Họ tên, Email, Số điện thoại
   - Nhấn "Lưu thay đổi"

3. **Đổi mật khẩu**
   - Trong trang Profile, chọn tab "Đổi mật khẩu"
   - Nhập mật khẩu cũ và mật khẩu mới
   - Nhấn "Cập nhật mật khẩu"

---

## 3. Quản lý Chương trình và Học phần

### 3.1 Quản lý Chương trình Đào tạo

#### 3.1.1 Xem danh sách chương trình
1. Từ menu chính, chọn **"Academics" → "Programs"**
2. Danh sách hiển thị tất cả chương trình với thông tin:
   - Tên chương trình
   - Khoa
   - Trạng thái (Đang hoạt động/Đã hoàn thành)
   - Ngày bắt đầu

#### 3.1.2 Tạo chương trình mới
1. **Truy cập trang tạo chương trình**
   - Từ trang danh sách chương trình, nhấn **"Tạo chương trình mới"**

2. **Nhập thông tin chương trình**
   ```
   Tên chương trình: [Ví dụ: Công nghệ Thông tin]
   Mô tả: [Mô tả chi tiết về chương trình]
   Khoa: [Chọn khoa từ dropdown]
   Ngày bắt đầu: [Chọn ngày]
   Phiên bản: [Ví dụ: 2024]
   ```

3. **Thiết lập PLO (Program Learning Outcomes)**
   - Nhấn **"Thêm PLO"**
   - Nhập mã PLO (ví dụ: PLO1, PLO2)
   - Nhập mô tả chi tiết
   - Chọn danh mục (Knowledge/Skill/Attitude)
   - Chọn mức độ Bloom (Remember/Understand/Apply/Analyze/Evaluate/Create)

4. **Thiết lập Performance Indicators (PI) - Tính năng mới**
   - Sau khi tạo PLO, nhấn **"Thêm PI"** cho từng PLO
   - Nhập mã PI (ví dụ: PLO1.1, PLO1.2)
   - Nhập mô tả chi tiết cho từng chỉ số đo lường
   - Mỗi PLO nên có ít nhất 2-3 PIs để đo lường đầy đủ

5. **Xem Ma trận Môn cốt lõi - Tính năng mới**
   - Truy cập tab **"Core Subject Matrix"**
   - Xem bảng ma trận hiển thị mối quan hệ giữa môn cốt lõi và PLO
   - Ma trận tự động cập nhật khi thêm môn học mới

6. **Lưu chương trình**
   - Kiểm tra lại thông tin
   - Nhấn **"Lưu chương trình"**

#### 3.1.3 Chỉnh sửa chương trình
1. Từ danh sách chương trình, nhấn biểu tượng **"Chỉnh sửa"** 
2. Cập nhật thông tin cần thiết
3. Nhấn **"Cập nhật"**

#### 3.1.4 Sao chép chương trình - Tính năng mới phiên bản 2.1
1. **Truy cập chi tiết chương trình**
   - Từ danh sách chương trình, nhấn vào tên chương trình
   - Hoặc nhấn biểu tượng **"Xem chi tiết"**

2. **Thực hiện sao chép**
   - Nhấn nút **"Sao chép chương trình"** ở góc phải trên
   - Xác nhận thao tác trong hộp thoại
   - Chờ hệ thống xử lý (có thể mất vài giây)

3. **Kết quả sao chép**
   - Chương trình mới được tạo với tên gốc + "(Copy)"
   - Tự động chuyển đến trang chi tiết chương trình mới
   - Thông báo chi tiết: số PLO, học phần, CLO, ánh xạ đã sao chép

4. **Dữ liệu được sao chép**
   - Program Learning Outcomes (PLOs)
   - Courses với quan hệ prerequisite
   - Course Learning Outcomes (CLOs)
   - Performance Indicators (PIs)
   - PLO-CLO mappings với đúng mức độ
   - Course Contents và Assessment Components

#### 3.1.5 Quản lý ánh xạ PLO-CLO - Tính năng nâng cao
1. **Truy cập trang chi tiết chương trình**
   - Xem section **"PLO Status Overview"**
   - Quan sát màu sắc trạng thái:
     - **Xanh lá + icon check**: PLO đã có ánh xạ với môn cốt lõi
     - **Đỏ + icon warning**: PLO chưa có ánh xạ với môn cốt lõi

2. **Xem thống kê tổng quan**
   - Số PLO đã ánh xạ vs tổng số PLO
   - Danh sách PLO với trạng thái và số môn cốt lõi đã ánh xạ

### 3.2 Quản lý Học phần

#### 3.2.1 Tạo học phần mới
1. **Truy cập quản lý học phần**
   - Chọn **"Academics" → "Courses"**
   - Nhấn **"Tạo học phần mới"**

2. **Nhập thông tin học phần**
   ```
   Chương trình: [Chọn chương trình đã tạo]
   Mã học phần: [Ví dụ: IT101]
   Tên học phần: [Ví dụ: Lập trình Python cơ bản]
   Số tín chỉ: [1-6 tín chỉ]
   Mô tả: [Mô tả chi tiết về học phần]
   Loại: [Bắt buộc/Tự chọn]
   ```

3. **Đánh dấu Môn cốt lõi - Tính năng mới**
   - Tích chọn **"Is Core Subject"** nếu môn học là môn cốt lõi
   - Môn cốt lõi sẽ hiển thị trong Core Subject Matrix của chương trình

4. **Thiết lập CLO (Course Learning Outcomes) - Quản lý tách riêng**
   - Truy cập tab **"CLO Management"**
   - Nhấn **"Thêm CLO"** với form riêng biệt
   - Nhập mã CLO (ví dụ: CLO1, CLO2)
   - Nhập mô tả cụ thể
   - Chọn trọng số (%)
   - Chọn phương pháp đánh giá
   - Thiết lập mức độ Bloom

5. **Thiết lập Lịch trình Môn học - Tính năng mới**
   - Truy cập tab **"Course Schedule"**
   - Thêm các Knowledge Blocks (chương/phần):
     ```
     Chương số: [1, 2, 3...]
     Tiêu đề: [Tên chương]
     Mô tả: [Nội dung chi tiết]
     Số giờ: [Số giờ dạy]
     CLO liên quan: [Chọn CLO từ dropdown]
     ```

6. **Thiết lập Assessment Components - Tính năng mới**
   - Truy cập tab **"Assessment Components"**
   - Thêm các thành phần đánh giá tách riêng:
     ```
     Loại: [Attendance, Assignment, Midterm, Final, Quiz, Project]
     Tên: [Tên cụ thể]
     Mô tả: [Chi tiết về assessment]
     Trọng số (%): [Phần trăm trong tổng điểm]
     ```
   - **Lưu ý**: Tổng trọng số phải bằng 100%

7. **Quản lý ánh xạ PLO-CLO - Tính năng nâng cao phiên bản 2.1**
   - Truy cập trang chi tiết CLO bằng cách nhấn vào tên CLO
   - Xem danh sách các PLO đã ánh xạ với CLO hiện tại
   - **Tạo ánh xạ mới**:
     - Nhấn **"Map CLO to PLO"**
     - Chọn PLO từ danh sách (chỉ hiển thị PLO cùng chương trình)
     - Chọn mức độ ánh xạ: Low/Medium/High
     - Nhấn **"Save Mapping"**
   - **Chỉnh sửa ánh xạ**:
     - Nhấn icon "Edit" bên cạnh ánh xạ hiện có
     - Thay đổi mức độ ánh xạ
     - Nhấn **"Update"**
   - **Xóa ánh xạ**:
     - Nhấn icon "Delete" bên cạnh ánh xạ
     - Xác nhận trong hộp thoại

#### 3.2.2 Quản lý lớp học
1. **Tạo lớp học**
   - Từ trang chi tiết học phần, nhấn **"Tạo lớp"**
   - Nhập thông tin:
     ```
     Tên lớp: [Ví dụ: IT101.01]
     Giảng viên: [Chọn từ danh sách]
     Học kỳ: [Chọn học kỳ]
     Năm học: [Ví dụ: 2024-2025]
     Sĩ số tối đa: [Số lượng sinh viên]
     ```

2. **Đăng ký sinh viên**
   - Chọn **"Quản lý sinh viên"** trong lớp
   - Import danh sách từ file Excel hoặc thêm thủ công
   - Xác nhận đăng ký

---

## 4. Quản lý Ngân hàng Câu hỏi

### 4.1 Tạo câu hỏi mới

#### 4.1.1 Truy cập trang tạo câu hỏi
1. Từ menu chính, chọn **"Question Bank" → "Create Question"**
2. Hoặc từ **"Questions" → "Add New Question"**

#### 4.1.2 Hệ thống phân quyền nâng cao - Phiên bản 2.1
**Quyền chỉnh sửa câu hỏi**:
- **Admin**: Có thể chỉnh sửa tất cả câu hỏi trong hệ thống
- **Giảng viên**: Có thể chỉnh sửa câu hỏi từ các học phần họ đang giảng dạy
- **Người tạo**: Có thể chỉnh sửa câu hỏi do họ tạo ra

**Cách hệ thống xác định quyền**:
- Kiểm tra user có phải admin không
- Kiểm tra user có giảng dạy course của câu hỏi không
- Kiểm tra user có phải người tạo câu hỏi không

#### 4.1.3 Nhập thông tin cơ bản
```
Học phần: [Chọn học phần bạn giảng dạy]
CLO: [Tự động load danh sách CLO của học phần]
Loại câu hỏi: [Multiple Choice/Essay/True-False/Short Answer/Practical]
Độ khó: [Easy/Medium/Hard]
Mức độ Bloom: [Remember/Understand/Apply/Analyze/Evaluate/Create]
```

#### 4.1.3 Tạo câu hỏi trắc nghiệm
1. **Nhập nội dung câu hỏi**
   ```
   Câu hỏi: Trong Python, hàm nào được sử dụng để in ra màn hình?
   ```

2. **Thêm các lựa chọn**
   ```
   A. print() ✓ [Đánh dấu đúng]
   B. display()
   C. show()
   D. output()
   ```

3. **Thêm giải thích**
   ```
   Giải thích: Hàm print() là hàm built-in trong Python để xuất 
   dữ liệu ra màn hình console.
   ```

#### 4.1.4 Tạo câu hỏi tự luận
1. **Nhập đề bài**
   ```
   Câu hỏi: Hãy giải thích khái niệm Object-Oriented Programming 
   và nêu ưu điểm của nó trong phát triển phần mềm.
   ```

2. **Thiết lập rubric đánh giá**
   - Nhấn **"Thêm tiêu chí đánh giá"**
   - Định nghĩa các tiêu chí:
     ```
     Tiêu chí 1: Hiểu biết khái niệm (40%)
     - Xuất sắc (4 điểm): Giải thích chính xác và đầy đủ
     - Tốt (3 điểm): Giải thích đúng với một số thiếu sót nhỏ
     - Đạt (2 điểm): Hiểu cơ bản nhưng chưa sâu
     - Chưa đạt (1 điểm): Hiểu sai hoặc không hiểu
     ```

#### 4.1.5 Thêm tags và hoàn tất
1. **Thêm thẻ phân loại**
   - Nhập tags: `python`, `basic`, `functions`
   - Tags giúp tìm kiếm và phân loại câu hỏi

2. **Xem trước và lưu**
   - Nhấn **"Preview"** để xem trước câu hỏi
   - Nhấn **"Save Question"** để lưu
   - Câu hỏi sẽ có trạng thái "Pending" chờ phê duyệt

### 4.2 Quản lý câu hỏi

#### 4.2.1 Tìm kiếm và lọc câu hỏi
1. **Sử dụng bộ lọc**
   ```
   Học phần: [Chọn học phần]
   CLO: [Chọn CLO cụ thể]
   Loại câu hỏi: [Chọn loại]
   Độ khó: [Chọn mức độ]
   Trạng thái: [Active/Pending/Rejected]
   ```

2. **Tìm kiếm theo từ khóa**
   - Nhập từ khóa trong hộp tìm kiếm
   - Tìm trong nội dung câu hỏi và tags

#### 4.2.2 Chỉnh sửa câu hỏi
1. Từ danh sách câu hỏi, nhấn **"Edit"**
2. Cập nhật nội dung cần thiết
3. Nhấn **"Update Question"**

#### 4.2.3 Quản lý tags
1. **Tạo tag mới**
   - Chọn **"Question Bank" → "Tags"**
   - Nhấn **"Create New Tag"**
   - Nhập tên và mô tả tag

2. **Gán tag cho câu hỏi**
   - Trong trang chỉnh sửa câu hỏi
   - Chọn tags từ danh sách có sẵn
   - Hoặc tạo tag mới trực tiếp

### 4.3 Import câu hỏi hàng loạt

#### 4.3.1 Chuẩn bị file Excel
1. **Download template**
   - Từ trang Question Bank, nhấn **"Download Import Template"**

2. **Điền thông tin theo format**
   ```
   Column A: Question Type (multiple_choice/essay/true_false)
   Column B: Course Code (IT101)
   Column C: CLO Code (CLO1)
   Column D: Question Content
   Column E: Option A (cho multiple choice)
   Column F: Option B
   Column G: Option C
   Column H: Option D
   Column I: Correct Answer (A/B/C/D)
   Column J: Explanation
   Column K: Difficulty (easy/medium/hard)
   Column L: Bloom Level
   Column M: Tags (separated by commas)
   ```

#### 4.3.2 Import file
1. **Upload file**
   - Chọn **"Question Bank" → "Import Questions"**
   - Chọn file Excel đã chuẩn bị
   - Nhấn **"Upload"**

2. **Xem trước và xác nhận**
   - Hệ thống sẽ hiển thị preview
   - Kiểm tra errors nếu có
   - Nhấn **"Confirm Import"**

---

## 5. Quản lý Đánh giá và Kiểm tra

### 5.1 Tạo bài kiểm tra

#### 5.1.1 Thiết lập thông tin cơ bản
1. **Truy cập trang tạo test**
   - Chọn **"Assessment" → "Create Test"**

2. **Nhập thông tin test - Với tính năng mới**
   ```
   Tên bài kiểm tra: [Ví dụ: Kiểm tra giữa kỳ - Python]
   Lớp học: [Chọn lớp từ dropdown]
   Hình thức thi: [Written/Oral/Online/Practical/Project/Presentation]
   Phương pháp đánh giá: [Rubric-based/Point-based/Percentage-based/Pass-Fail/Letter Grade]
   Thời gian làm bài: [Phút]
   Tổng điểm: [Ví dụ: 100]
   Ngày thi: [Chọn ngày]
   Thời gian bắt đầu: [Giờ:phút]
   Thời gian kết thúc: [Giờ:phút]
   ```

#### 5.1.2 Chọn câu hỏi cho bài test - Hỗ trợ Multi-CLO
1. **Thêm câu hỏi thủ công**
   - Nhấn **"Add Questions"**
   - Lọc câu hỏi theo CLO và độ khó
   - Chọn câu hỏi và nhấn **"Add to Test"**
   - **Thiết lập Multi-CLO Support - Tính năng mới**:
     ```
     Target CLOs: [Chọn 1 hoặc nhiều CLO]
     CLO1 Points: [Điểm đáp ứng cho CLO1]
     CLO2 Points: [Điểm đáp ứng cho CLO2]
     Total Points: [Tổng điểm câu hỏi]
     ```
   - Một câu hỏi có thể đáp ứng nhiều CLO với phân bổ điểm khác nhau

2. **Tạo test tự động**
   - Nhấn **"Auto Generate"**
   - Thiết lập tỷ lệ câu hỏi:
     ```
     CLO1: 30% (3 câu)
     CLO2: 40% (4 câu)  
     CLO3: 30% (3 câu)
     
     Easy: 30%
     Medium: 50%
     Hard: 20%
     ```

#### 5.1.3 Cấu hình bài test
1. **Thiết lập quy tắc**
   ```
   Số lần làm bài: [1-3 lần]
   Xáo trộn câu hỏi: [Có/Không]
   Xáo trộn đáp án: [Có/Không]
   Hiển thị kết quả ngay: [Có/Không]
   Cho phép xem lại: [Có/Không]
   ```

2. **Xuất bản test**
   - Xem trước toàn bộ bài test
   - Nhấn **"Publish Test"**

### 5.2 Chấm bài và quản lý kết quả

#### 5.2.1 Chấm bài tự động (Trắc nghiệm)
- Hệ thống tự động chấm các câu trắc nghiệm
- Kết quả hiển thị ngay sau khi sinh viên nộp bài

#### 5.2.2 Chấm bài thủ công (Tự luận)
1. **Truy cập danh sách bài thi**
   - Chọn **"Assessment" → "Test Results"**
   - Chọn bài test cần chấm

2. **Chấm từng bài**
   - Nhấn **"Grade"** bên cạnh tên sinh viên
   - Đọc câu trả lời của sinh viên
   - Sử dụng rubric để đánh giá:
     ```
     Tiêu chí 1: Hiểu biết khái niệm
     [Chọn mức độ: Xuất sắc/Tốt/Đạt/Chưa đạt]
     
     Tiêu chí 2: Khả năng phân tích
     [Chọn mức độ và điều chỉnh điểm nếu cần]
     ```
   - Thêm nhận xét cho sinh viên
   - Nhấn **"Save Grade"**

#### 5.2.3 Xuất kết quả
1. **Xuất điểm cho từng sinh viên**
   - Chọn **"Export Grades"**
   - Chọn format: Excel/PDF/CSV

2. **Gửi kết quả cho sinh viên**
   - Nhấn **"Send Results to Students"**
   - Sinh viên sẽ nhận email thông báo

---

## 6. Báo cáo và Phân tích

### 6.1 Báo cáo CLO Performance

#### 6.1.1 Tạo báo cáo CLO
1. **Truy cập module báo cáo**
   - Chọn **"Reports" → "CLO Reports"**

2. **Thiết lập tham số báo cáo**
   ```
   Học phần: [Chọn học phần]
   Lớp học: [Chọn lớp cụ thể hoặc tất cả]
   Kỳ học: [Chọn kỳ học]
   CLO: [Chọn CLO cụ thể hoặc tất cả]
   ```

3. **Tạo báo cáo**
   - Nhấn **"Generate Report"**
   - Chờ hệ thống xử lý (có thể mất 30 giây)

#### 6.1.2 Phân tích kết quả CLO
**Báo cáo sẽ hiển thị**:
- Tỷ lệ đạt CLO của từng sinh viên
- Điểm trung bình theo CLO
- Phân bố điểm số
- Xu hướng theo thời gian
- Nhận xét và đề xuất cải thiện

#### 6.1.3 Xuất báo cáo
1. **Export PDF**
   - Nhấn **"Export PDF"** để tải báo cáo định dạng PDF
   - Thích hợp cho in ấn và lưu trữ

2. **Export Excel**
   - Nhấn **"Export Excel"** để tải dữ liệu chi tiết
   - Thích hợp cho phân tích thêm

### 6.2 Phân tích bài kiểm tra

#### 6.2.1 Dashboard phân tích test
1. **Truy cập test analysis**
   - Từ **"Assessment" → "Test Analysis"**
   - Chọn bài test cần phân tích

2. **Xem thống kê tổng quan**
   ```
   Tổng số sinh viên: [Số lượng]
   Điểm trung bình: [Điểm/Tổng điểm]
   Độ lệch chuẩn: [Giá trị]
   Điểm cao nhất: [Điểm]
   Điểm thấp nhất: [Điểm]
   ```

#### 6.2.2 Phân tích từng câu hỏi
**Cho mỗi câu hỏi, báo cáo hiển thị**:
- Tỷ lệ trả lời đúng (%)
- Chỉ số độ khó (Difficulty Index)
- Chỉ số phân biệt (Discrimination Index)
- Phân tích distractor (câu trả lời sai)
- Thời gian trung bình làm bài

#### 6.2.3 Đề xuất cải thiện
Hệ thống tự động đưa ra đề xuất:
- Câu hỏi cần điều chỉnh (quá dễ/quá khó)
- Distractor không hiệu quả
- CLO cần tăng cường

### 6.3 Báo cáo tùy chỉnh

#### 6.3.1 Tạo báo cáo theo yêu cầu
1. **Truy cập Custom Reports**
   - Chọn **"Reports" → "Custom Reports"**

2. **Chọn dữ liệu cần báo cáo**
   ```
   Data Source: [Students/Questions/Tests/CLOs]
   Time Period: [Từ ngày - đến ngày]
   Filters: [Các bộ lọc tùy chỉnh]
   Metrics: [Chọn các chỉ số cần hiển thị]
   ```

3. **Thiết lập định dạng**
   - Chọn loại biểu đồ: Bar/Line/Pie chart
   - Thiết lập màu sắc và nhãn
   - Preview trước khi tạo

#### 6.3.2 Lưu template báo cáo
1. Sau khi tạo báo cáo, nhấn **"Save as Template"**
2. Đặt tên template và mô tả
3. Sử dụng lại cho các kỳ sau

---

## 7. Tính năng Nâng cao

### 7.1 Quản lý Workflow Phê duyệt

#### 7.1.1 Thiết lập quy trình phê duyệt
1. **Cấu hình workflow**
   - Admin có thể thiết lập quy trình phê duyệt câu hỏi
   - Định nghĩa các level approval
   - Gán người phê duyệt cho từng level

2. **Quy trình mặc định**
   ```
   Bước 1: Giảng viên tạo câu hỏi → Status: Draft
   Bước 2: Submit for review → Status: Pending
   Bước 3: Department Head review → Status: Approved/Rejected
   Bước 4: Nếu approved → Status: Active
   ```

#### 7.1.2 Xử lý phê duyệt
1. **Đối với người phê duyệt**
   - Truy cập **"Pending Approvals"**
   - Review chi tiết câu hỏi
   - Approve hoặc Reject với comment
   - Gửi thông báo cho tác giả

2. **Đối với tác giả**
   - Nhận notification khi có phê duyệt
   - Nếu rejected, chỉnh sửa và resubmit
   - Track trạng thái qua **"My Questions"**

### 7.2 Hệ thống Thông báo

#### 7.2.1 Loại thông báo
- **Email notifications**: Gửi qua email
- **In-app notifications**: Hiển thị trong hệ thống
- **SMS notifications**: Gửi SMS (tương lai)

#### 7.2.2 Cấu hình thông báo
1. **Truy cập Notification Settings**
   - Từ Profile → Notification Preferences

2. **Chọn loại thông báo muốn nhận**
   ```
   ✓ Question approval status
   ✓ Test results available  
   ✓ New assignments
   ✓ System maintenance
   ✓ Weekly summary reports
   ```

### 7.3 Backup và Khôi phục

#### 7.3.1 Backup dữ liệu
1. **Automatic backup**
   - Hệ thống tự động backup hàng ngày
   - Lưu trữ backup trong 30 ngày

2. **Manual backup**
   - Admin có thể tạo backup thủ công
   - Export dữ liệu theo module hoặc toàn bộ

#### 7.3.2 Khôi phục dữ liệu
1. **Restore from backup**
   - Chỉ Admin có quyền restore
   - Chọn backup point cần restore
   - Xác nhận và chờ quá trình hoàn tất

---

## 8. Xử lý Sự cố

### 8.1 Sự cố thường gặp

#### 8.1.1 Không thể đăng nhập
**Triệu chứng**: Nhập đúng username/password nhưng không vào được
**Giải pháp**:
1. Kiểm tra Caps Lock
2. Xóa cache và cookies trình duyệt
3. Thử trình duyệt khác
4. Liên hệ IT support nếu vẫn lỗi

#### 8.1.2 Trang load chậm
**Triệu chứng**: Trang mất > 10 giây để tải
**Giải pháp**:
1. Kiểm tra kết nối internet
2. Reload trang (Ctrl+F5)
3. Thử incognito mode
4. Báo cáo cho IT team

#### 8.1.3 Mất dữ liệu form
**Triệu chứng**: Dữ liệu biến mất khi điền form dài
**Giải pháp**:
1. Sử dụng tính năng auto-save
2. Copy nội dung quan trọng trước khi submit
3. Không mở nhiều tab cùng lúc
4. Save draft thường xuyên

#### 8.1.4 Lỗi upload file
**Triệu chứng**: Không upload được file hoặc file upload bị lỗi
**Giải pháp**:
1. Kiểm tra kích thước file (< 10MB)
2. Kiểm tra định dạng file được hỗ trợ
3. Đổi tên file không có ký tự đặc biệt
4. Thử upload file khác để test

### 8.2 Thông báo lỗi và ý nghĩa

| Mã lỗi | Thông báo | Nguyên nhân | Giải pháp |
|---------|-----------|-------------|-----------|
| AUTH-001 | Invalid credentials | Sai username/password | Kiểm tra lại thông tin đăng nhập |
| PERM-002 | Access denied | Không có quyền truy cập | Liên hệ admin cấp quyền |
| DATA-003 | Validation error | Dữ liệu nhập không hợp lệ | Kiểm tra format dữ liệu |
| FILE-004 | Upload failed | Lỗi upload file | Kiểm tra file size và format |
| CONN-005 | Connection timeout | Mất kết nối server | Reload trang và thử lại |

### 8.3 Liên hệ hỗ trợ

#### 8.3.1 Thông tin liên hệ
```
IT Support Team:
Email: <EMAIL>
Phone: (84) 24-xxxx-xxxx
Working hours: 8:00 - 17:00 (Mon-Fri)

Emergency contact:
Phone: (84) 9x-xxxx-xxxx (24/7)
```

#### 8.3.2 Cách báo cáo lỗi
1. **Chuẩn bị thông tin**
   - Thời gian xảy ra lỗi
   - Tài khoản đang sử dụng
   - Trình duyệt và phiên bản
   - Mô tả chi tiết bước tái tạo lỗi
   - Screenshot nếu có

2. **Gửi báo cáo**
   - Email với subject: [CLO System] Bug Report
   - Hoặc tạo ticket qua hệ thống helpdesk
   - Đính kèm screenshot và log files

---

## 9. Mẹo và Thủ thuật

### 9.1 Tăng hiệu quả làm việc

#### 9.1.1 Keyboard shortcuts
```
Ctrl + S: Save (trong forms)
Ctrl + Enter: Submit form nhanh
Esc: Đóng modal/popup
Tab: Di chuyển giữa các fields
F5: Refresh trang
Ctrl + F: Tìm kiếm trong trang
```

#### 9.1.2 Sử dụng templates
1. **Question templates**
   - Tạo template cho từng loại câu hỏi
   - Save time khi tạo câu hỏi similar
   - Đảm bảo consistency

2. **Test templates**
   - Tạo template cho từng loại bài test
   - Reuse cho các kỳ học sau
   - Đảm bảo cấu trúc nhất quán

#### 9.1.3 Batch operations
1. **Bulk question import**
   - Sử dụng Excel template
   - Import nhiều câu hỏi cùng lúc
   - Tiết kiệm thời gian đáng kể

2. **Bulk grade export**
   - Export tất cả điểm trong một lần
   - Integrate với hệ thống quản lý khác

### 9.2 Best practices

#### 9.2.1 Quản lý câu hỏi
1. **Đặt tên có hệ thống**
   - Sử dụng prefix: [Course Code]_[Topic]_[Number]
   - Ví dụ: IT101_Variables_001

2. **Sử dụng tags hiệu quả**
   - Tag theo topic: `variables`, `functions`, `loops`
   - Tag theo difficulty: `beginner`, `intermediate`, `advanced`
   - Tag theo exam type: `midterm`, `final`, `quiz`

3. **Regular review và update**
   - Review câu hỏi sau mỗi kỳ thi
   - Update dựa trên statistical analysis
   - Archive câu hỏi outdated

#### 9.2.2 Thiết kế bài test
1. **Balanced distribution**
   - 30% easy, 50% medium, 20% hard
   - Cover tất cả CLOs proportionally
   - Mix các loại câu hỏi khác nhau

2. **Time management**
   - Estimate 1-2 phút cho multiple choice
   - 10-15 phút cho short answer  
   - 20-30 phút cho essay question

#### 9.2.3 Phân tích kết quả
1. **Regular monitoring**
   - Check weekly statistics
   - Identify struggling students sớm
   - Adjust teaching methods accordingly

2. **Data-driven decisions**
   - Use statistical analysis để improve questions
   - Identify knowledge gaps
   - Plan remedial activities

---

## 10. Phụ lục

### 10.1 Danh sách format file được hỗ trợ

#### 10.1.1 Question import
- **Excel**: .xlsx, .xls
- **CSV**: .csv (UTF-8 encoding)
- **Text**: .txt (tab-delimited)

#### 10.1.2 Image upload
- **Formats**: .jpg, .jpeg, .png, .gif
- **Size limit**: 5MB per image
- **Dimensions**: Recommend 800x600 max

#### 10.1.3 Document upload
- **Formats**: .pdf, .doc, .docx
- **Size limit**: 10MB per file
- **Note**: PDF preferred cho better compatibility

### 10.2 Browser requirements detail

| Browser | Minimum Version | Recommended | Notes |
|---------|----------------|-------------|-------|
| Chrome | 90 | Latest | Best performance |
| Firefox | 88 | Latest | Good compatibility |
| Safari | 14 | Latest | Mac users |
| Edge | 90 | Latest | Windows users |
| IE | Not supported | - | Please upgrade |

### 10.3 Sample Excel templates

#### 10.3.1 Question import template
```
Column A: question_type (multiple_choice/essay/true_false/short_answer)
Column B: course_code (IT101)
Column C: clo_code (CLO1)
Column D: content (Question text)
Column E: option_a (For MC questions)
Column F: option_b
Column G: option_c  
Column H: option_d
Column I: correct_answer (A/B/C/D)
Column J: explanation
Column K: difficulty (easy/medium/hard)
Column L: bloom_level (remember/understand/apply/analyze/evaluate/create)
Column M: tags (comma separated)
```

#### 10.3.2 Student enrollment template
```
Column A: student_id
Column B: first_name
Column C: last_name
Column D: email
Column E: program_code
Column F: year (1/2/3/4)
```

### 10.4 API endpoints (for developers)

#### 10.4.1 Authentication
```
POST /api/auth/login/
POST /api/auth/logout/
GET  /api/auth/user/
```

#### 10.4.2 Courses
```
GET    /api/courses/
POST   /api/courses/
GET    /api/courses/{id}/
PUT    /api/courses/{id}/
DELETE /api/courses/{id}/
GET    /api/courses/{id}/clos/
```

#### 10.4.3 Questions
```
GET    /api/questions/
POST   /api/questions/
GET    /api/questions/{id}/
PUT    /api/questions/{id}/
DELETE /api/questions/{id}/
POST   /api/questions/import/
```

---

## Kết luận

Hướng dẫn này cung cấp đầy đủ thông tin để sử dụng hiệu quả CLO Management System. Để được hỗ trợ thêm, vui lòng liên hệ với đội ngũ IT Support hoặc tham khảo documentation online tại website của hệ thống.

**Lưu ý**: Tài liệu này sẽ được cập nhật thường xuyên theo các phiên bản mới của hệ thống. Vui lòng kiểm tra phiên bản mới nhất trên website chính thức.