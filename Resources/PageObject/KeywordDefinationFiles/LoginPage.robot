*** Keywords ***
Login With Credentials
    Maximize Browser Window
    Sleep    4s
    # Check if toolbar element exists, if it exists then click hide
    ${IS_VISIBLE}=    Run Keyword And Return Status    Element Should Be Visible    ${HIDE_TOOLBAR}
    Run Keyword If    ${IS_VISIBLE}    Click Element    ${HIDE_TOOLBAR}

    [Arguments]    ${username}    ${password}
    Input Text    ${USERNAME_INPUT}    ${username}
    Input Text    ${PASSWORD_INPUT}    ${password}
    Click Button  ${LOGIN_BUTTON}


Get Cookie Count
    ${cookies}=    Get Cookies
    ${count}=      Get Length    ${cookies}
    [Return]       ${count}


Logout Normal
    Click Element    ${SETTING_BUTTON}
    Sleep    2s
    Click Element    ${LOGOUT_BUTTON}
    Wait Until Page Contains    Đăng nhập    10s
