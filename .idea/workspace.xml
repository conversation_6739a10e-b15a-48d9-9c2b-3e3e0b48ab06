<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f0935937-c41b-482e-bd5d-bfded00a82ba" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/Resources/PageObject/KeywordDefinationFiles/LoginPage.robot" beforeDir="false" afterPath="$PROJECT_DIR$/Resources/PageObject/KeywordDefinationFiles/LoginPage.robot" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Resources/PageObject/Locators/LoginLocators.robot" beforeDir="false" afterPath="$PROJECT_DIR$/Resources/PageObject/Locators/LoginLocators.robot" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Tests/Login/AdminLoginTest.robot" beforeDir="false" afterPath="$PROJECT_DIR$/Tests/Login/AdminLoginTest.robot" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Tests/Login/log.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Tests/Login/output.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Tests/Login/report.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Tests/Login/selenium-screenshot-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Tests/Login/selenium-screenshot-2.png" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/Resources/PageObject/KeywordDefinationFiles/LoginPage.robot" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Resources/PageObject/Locators/LoginLocators.robot" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Tests/Login/AdminLoginTest.robot" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Tests/Login/DeanLoginTest.robot" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="2zOAnzZ3nohV3yAFRRpA6rcRx5w" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "/home/<USER>/Desktop/eme/learn-auto-eme",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-PY-243.26053.29" />
        <option value="bundled-python-sdk-b1dbf8ef85a6-4df51de95216-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.26053.29" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f0935937-c41b-482e-bd5d-bfded00a82ba" name="Changes" comment="" />
      <created>1751591916874</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751591916874</updated>
      <workItem from="1751591918006" duration="6614000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>